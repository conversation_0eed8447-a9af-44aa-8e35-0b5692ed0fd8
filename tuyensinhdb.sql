-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3307
-- Generation Time: Jun 19, 2025 at 05:09 PM
-- Server version: 8.0.30
-- PHP Version: 8.2.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `tuyensinhdb`
--

-- --------------------------------------------------------

--
-- Table structure for table `admission_methods`
--

CREATE TABLE `admission_methods` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tên phương thức xét tuyển',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mã phương thức',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'Mô tả chi tiết',
  `requires_subject_combinations` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Có cần tổ hợp môn không',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Trạng thái hoạt động',
  `priority_order` int NOT NULL DEFAULT '0' COMMENT 'Thứ tự ưu tiên',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admission_methods`
--

INSERT INTO `admission_methods` (`id`, `name`, `code`, `description`, `requires_subject_combinations`, `is_active`, `priority_order`, `created_at`, `updated_at`) VALUES
(1, 'Xét kết quả kỳ thi tốt nghiệp THPT', 'THPT', 'Xét tuyển dựa trên điểm thi tốt nghiệp THPT theo tổ hợp môn', 1, 1, 1, '2025-06-16 03:21:07', '2025-06-16 03:21:07'),
(2, 'Xét kết quả học tập THPT (Học bạ)', 'HOCBA', 'Xét tuyển dựa trên kết quả học tập 3 năm THPT', 0, 1, 2, '2025-06-16 03:21:07', '2025-06-16 03:21:07'),
(3, 'Xét Điểm ĐGNL HCM', 'DGNL_HCM', 'Xét tuyển dựa trên điểm Đánh giá năng lực Đại học Quốc gia TP.HCM', 0, 1, 3, '2025-06-16 03:21:07', '2025-06-16 03:21:07'),
(4, 'Xét Điểm Đánh giá đầu vào V-SAT', 'VSAT', 'Xét tuyển dựa trên điểm Vietnam Scholastic Assessment Test', 0, 1, 4, '2025-06-16 03:21:07', '2025-06-16 03:21:07');

-- --------------------------------------------------------

--
-- Table structure for table `admission_scores`
--

CREATE TABLE `admission_scores` (
  `id` bigint UNSIGNED NOT NULL,
  `major_id` bigint UNSIGNED NOT NULL,
  `admission_method_id` bigint UNSIGNED NOT NULL,
  `subject_combination_id` bigint UNSIGNED DEFAULT NULL,
  `year` year NOT NULL COMMENT 'Năm tuyển sinh',
  `score` decimal(4,2) NOT NULL COMMENT 'Điểm chuẩn',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admission_scores`
--

INSERT INTO `admission_scores` (`id`, `major_id`, `admission_method_id`, `subject_combination_id`, `year`, `score`, `created_at`, `updated_at`) VALUES
(6, 14, 1, NULL, 2025, '20.00', '2025-06-16 04:00:23', '2025-06-16 04:00:23'),
(7, 14, 1, NULL, 2024, '19.00', '2025-06-16 04:00:23', '2025-06-16 04:00:23'),
(8, 14, 1, NULL, 2023, '18.00', '2025-06-16 04:00:23', '2025-06-16 04:00:23'),
(9, 14, 1, NULL, 2022, '17.00', '2025-06-16 04:00:23', '2025-06-16 04:00:23'),
(10, 14, 1, NULL, 2021, '16.00', '2025-06-16 04:00:23', '2025-06-16 04:00:23'),
(16, 18, 1, NULL, 2025, '20.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(17, 18, 1, NULL, 2024, '21.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(18, 18, 1, NULL, 2023, '22.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(19, 18, 1, NULL, 2022, '23.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(20, 18, 1, NULL, 2021, '24.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(21, 18, 4, NULL, 2025, '21.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(22, 18, 4, NULL, 2024, '22.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(23, 18, 4, NULL, 2023, '23.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(24, 18, 4, NULL, 2022, '24.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(25, 18, 4, NULL, 2021, '25.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(26, 18, 3, NULL, 2025, '22.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(27, 18, 3, NULL, 2024, '23.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(28, 18, 3, NULL, 2023, '24.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(29, 18, 3, NULL, 2022, '25.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(30, 18, 3, NULL, 2021, '26.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(31, 18, 2, NULL, 2025, '23.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(32, 18, 2, NULL, 2024, '24.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(33, 18, 2, NULL, 2023, '25.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(34, 18, 2, NULL, 2022, '26.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(35, 18, 2, NULL, 2021, '27.00', '2025-06-16 04:48:35', '2025-06-16 04:48:35');

-- --------------------------------------------------------

--
-- Table structure for table `blogs`
--

CREATE TABLE `blogs` (
  `id` bigint UNSIGNED NOT NULL,
  `name_blog` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `image_blog` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `description_blog` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `view_blog` int NOT NULL DEFAULT '0',
  `content_blog` longtext COLLATE utf8mb4_general_ci NOT NULL,
  `author_id` bigint UNSIGNED NOT NULL,
  `category_blog_id` bigint UNSIGNED NOT NULL,
  `date_blog` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status_blog` tinyint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `blogs`
--

INSERT INTO `blogs` (`id`, `name_blog`, `image_blog`, `description_blog`, `view_blog`, `content_blog`, `author_id`, `category_blog_id`, `date_blog`, `status_blog`) VALUES
(1, 'Thông báo tuyển sinh năm học mới tại Đại học Trà Vinhh', '/imagesSource/images.jpeg', 'Thông tin chính thức về kỳ tuyển sinh sắp tới của Đại học Trà Vinh.', 6, 'abc', 2, 1, '2025-05-18 15:46:50', 0),
(2, 'Hướng dẫn đăng ký xét tuyển trực tuyến vào Đại học Trà Vinh', '/imagesSource/1748770440_350928773.jpg', 'Chi tiết các bước đăng ký xét tuyển trực tuyến tại Đại học Trà Vinh.', 3, 'abc', 2, 2, '2025-05-18 15:46:50', 0),
(3, 'Đại học Trà Vinh tổ chức ngày hội tư vấn tuyển sinh', '/imagesSource/1748769568_images.jpeg', 'Tin tức mới nhất từ nhà trường về các hoạt động tư vấn tuyển sinh.', 2, 'abcxxw', 2, 2, '2025-05-18 15:46:50', 0),
(4, 'Những câu hỏi thường gặp khi đăng ký vào Đại học Trà Vinh', '/imagesSource/images.jpeg', 'Tổng hợp các thắc mắc phổ biến của thí sinh về tuyển sinh tại Đại học Trà Vinh.', 1, 'abc', 2, 4, '2025-05-18 15:46:50', 0),
(5, '5 kinh nghiệm xét tuyển hiệu quả vào Đại học Trà Vinh', '/imagesSource/images.jpeg', 'Chia sẻ những bí quyết giúp tăng cơ hội trúng tuyển vào Đại học Trà Vinh.', 0, 'abc', 2, 5, '2025-05-18 15:46:50', 0),
(14, 'Thông tin tuyển sinh', '/imagesSource/1749738514_Trang chủ 1.png', 'Đây là thông tin tuyển sinh', 3, '<p>abcxyz</p>', 2, 1, '2025-06-12 21:28:34', 0);

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `category_blog`
--

CREATE TABLE `category_blog` (
  `id` bigint UNSIGNED NOT NULL,
  `name_category_blog` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `image_category_blog` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status_category_blog` tinyint NOT NULL DEFAULT '0' COMMENT '0 is enabled, 1 is disabled'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `category_blog`
--

INSERT INTO `category_blog` (`id`, `name_category_blog`, `image_category_blog`, `status_category_blog`) VALUES
(1, 'Thông báo tuyển sinh', NULL, 0),
(2, 'Hướng dẫn đăng ký', NULL, 0),
(3, 'Tin tức nhà trường', NULL, 0),
(4, 'Câu hỏi thường gặp', NULL, 0),
(5, 'Kinh nghiệm xét tuyển', NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `category_major`
--

CREATE TABLE `category_major` (
  `id` bigint UNSIGNED NOT NULL,
  `name_category_major` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `image_category_major` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status_category_major` tinyint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `category_major`
--

INSERT INTO `category_major` (`id`, `name_category_major`, `image_category_major`, `status_category_major`) VALUES
(1, 'Kinh tế - Luật', NULL, 0),
(2, 'Khoa Luật', NULL, 0),
(3, 'Khoa Ngôn ngữ – Văn hóa – Nghệ thuật Khmer Nam Bộ', NULL, 0),
(4, 'Khoa Dược', NULL, 0),
(5, 'Khoa Logistics', NULL, 0),
(6, 'Khoa Quản lý Nhà nước – Quản trị Văn phòng', NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `chat_ai`
--

CREATE TABLE `chat_ai` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `message_content` longtext COLLATE utf8mb4_general_ci NOT NULL,
  `type_message` tinyint NOT NULL DEFAULT '0',
  `date_message` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `chat_ai`
--

INSERT INTO `chat_ai` (`id`, `user_id`, `message_content`, `type_message`, `date_message`) VALUES
(1, 3, 'Tôi học giỏi toán thì nên học ngành gì', 0, '2025-05-21 09:57:35'),
(2, 3, 'Nếu bạn học giỏi toán, có thể xem xét những ngành học sau tại Đại học Trà Vinh mà có sự liên quan mật thiết đến toán học:\n\n1. **Ngành Toán ứng dụng**: Ngành này sẽ giúp bạn vận dụng các kiến thức toán học vào thực tiễn, phù hợp cho những ai yêu thích giải quyết các vấn đề thông qua toán học.\n  \n2. **Ngành Kinh tế**: Những kiến thức về toán học rất quan trọng trong phân tích dữ liệu và ra quyết định trong lĩnh vực kinh tế.\n\n3. **Ngành Khoa học máy tính**: Ngành này đòi hỏi sự tư duy logic tốt và nhiều khái niệm toán học trong lập trình và phát triển phần mềm.\n\n4. **Ngành Thống kê**: Ngành này sẽ giúp bạn áp dụng toán học vào việc thu thập, phân tích và diễn giải dữ liệu.\n\nNếu bạn có đam mê và sở thích trong những lĩnh vực này, có thể xem xét và tìm hiểu thêm về chúng nhé!', 1, '2025-05-21 09:57:35'),
(3, 3, 'Còn nếu tôi học giỏi văn thì sao', 0, '2025-05-21 09:59:10'),
(4, 3, 'Nếu bạn học giỏi môn Văn, có thể bạn sẽ phù hợp với các ngành học liên quan đến ngôn ngữ, văn hóa, xã hội hoặc truyền thông. Tại Đại học Trà Vinh, các ngành học như Ngữ văn, Giáo dục Tiểu học, hay các ngành liên quan đến truyền thông có thể là sự lựa chọn tốt cho bạn. \n\nHọc giỏi Văn thường cho thấy bạn có khả năng tư duy sáng tạo, viết lách và phân tích văn bản, điều này rất quan trọng trong những lĩnh vực trên. Hãy cân nhắc xem sở thích và đam mê của bạn trong môn học này để chọn được ngành phù hợp nhất nhé!', 1, '2025-05-21 09:59:10'),
(5, 3, 'Tôi còn học giỏi tiếng anh nữa', 0, '2025-05-21 10:13:21'),
(6, 3, 'Rất tốt! Nếu bạn yêu thích tiếng Anh và có khả năng học giỏi môn này, bạn có thể xem xét các ngành học liên quan đến ngoại ngữ tại Đại học Trà Vinh, như ngành Ngôn ngữ Anh hoặc những ngành nghề khác cần sử dụng tiếng Anh. Ngoài ra, cũng có thể tìm hiểu về các ngành như Du lịch, Quản trị Kinh doanh hoặc Quan hệ Quốc tế, nơi mà kỹ năng tiếng Anh là vô cùng quan trọng. Bạn có thể cho tôi biết thêm về sở thích hoặc mục tiêu của mình để tôi có thể tư vấn chính xác hơn nhé!', 1, '2025-05-21 10:13:21'),
(7, 4, 'hello', 0, '2025-06-07 12:49:45'),
(8, 4, 'Chào bạn! Nếu bạn có bất kỳ câu hỏi nào liên quan đến ngành học tại Đại học Trà Vinh, hãy chia sẻ nhé! Tôi sẵn sàng giúp bạn tìm hiểu về các ngành học phù hợp với sở thích và đam mê của bạn.', 1, '2025-06-07 12:49:45'),
(9, 4, 'bạn có thể trả lời các câu hỏi khác ngoài các ngành học không?', 0, '2025-06-07 12:50:17'),
(10, 4, 'Xin lỗi, nhưng tôi chỉ trả lời các câu hỏi liên quan đến ngành học, dựa trên sở thích, đam mê môn học hoặc đặc điểm phù hợp với từng ngành. Đại Học Trà Vinh đang đào tạo nhiều ngành học đa dạng, hãy hỏi về những ngành học này nhé!', 1, '2025-06-07 12:50:17'),
(11, 4, 'nếu tôi học giỏi môn Văn thì nên chọn ngành nào?', 0, '2025-06-14 10:39:59'),
(12, 4, 'Nếu bạn học giỏi môn Văn, bạn có thể cân nhắc đến một số ngành học Đại học Trà Vinh như sau:\n\n1. **Ngành Sư phạm Ngữ văn**: Nếu bạn đam mê giảng dạy và truyền đạt kiến thức về văn chương cho thế hệ sau.\n   \n2. **Ngành Văn học**: Dành cho những ai yêu thích nghiên cứu, phân tích và khám phá các tác phẩm văn học cũng như lịch sử văn học.\n\n3. **Ngành Ngôn ngữ Anh**:Nếu bạn có hứng thú với việc học ngoại ngữ và thích khía cạnh giao tiếp văn hóa qua ngôn ngữ.\n\nCác ngành học này đều có liên quan mật thiết đến năng khiếu và niềm đam mê của bạn với môn Văn. Hãy cân nhắc để chọn cho mình ngành học phù hợp nhất nhé!', 1, '2025-06-14 10:39:59'),
(13, 4, 'Công nghệ thông tin', 0, '2025-06-14 10:40:34'),
(14, 4, 'Ngành Công nghệ thông tin là một trong những ngành học được đào tạo tại Đại học Trà Vinh. Nếu bạn có đam mê với công nghệ, thích lập trình, phát triển phần mềm hoặc quản lý hệ thống thông tin, đây có thể là sự lựa chọn phù hợp cho bạn. Ngành này giúp bạn trang bị kiến thức và kỹ năng cần thiết để làm việc trong các lĩnh vực như phát triển ứng dụng, an ninh mạng, hoặc quản trị cơ sở dữ liệu.\n\nNếu bạn cần thêm thông tin cụ thể về chương trình học hoặc cơ hội nghề nghiệp sau khi tốt nghiệp ngành Công nghệ thông tin tại Đại học Trà Vinh, hãy cho tôi biết nhé!', 1, '2025-06-14 10:40:34'),
(15, 4, 'Hãy cho tôi biết', 0, '2025-06-14 10:41:08'),
(16, 4, 'Xin lỗi bạn, nhưng tôi chỉ có thể trả lời các câu hỏi liên quan đến ngành học tại Đại học Trà Vinh. Bạn có thể hỏi về sở thích, đam mê môn học hoặc đặc điểm phù hợp với từng ngành học mà trường đang đào tạo nhé!', 1, '2025-06-14 10:41:08'),
(17, 4, 'tôi cần thêm thông tin cụ thể về chương trình học hoặc cơ hội nghề nghiệp sau khi tốt nghiệp ngành Công nghệ thông tin tại Đại học Trà Vinh', 0, '2025-06-14 10:41:54'),
(18, 4, 'Tại Đại học Trà Vinh, ngành Công nghệ thông tin được thiết kế để trang bị cho sinh viên những kiến thức và kỹ năng cần thiết trong lĩnh vực công nghệ thông tin. Chương trình học bao gồm các môn học về lập trình, quản trị cơ sở dữ liệu, phát triển phần mềm, mạng máy tính, an toàn thông tin và nhiều môn học khác liên quan đến ứng dụng công nghệ trong các lĩnh vực khác nhau.\n\nSau khi tốt nghiệp, sinh viên ngành Công nghệ thông tin có nhiều cơ hội nghề nghiệp, chẳng hạn như:\n\n1. Lập trình viên phần mềm.\n2. Kỹ sư mạng.\n3. Chuyên viên an ninh mạng.\n4. Quản trị hệ thống thông tin.\n5. Phát triển ứng dụng di động.\n6. Chuyên viên phân tích dữ liệu.\n\nNgành Công nghệ thông tin không chỉ giúp sinh viên phát triển kỹ năng chuyên môn mà còn trang bị cho họ khả năng sáng tạo và giải quyết vấn đề, điều này rất phù hợp với sự phát triển nhanh chóng của công nghệ trong xã hội hiện nay.', 1, '2025-06-14 10:41:54');

-- --------------------------------------------------------

--
-- Table structure for table `cities`
--

CREATE TABLE `cities` (
  `id` bigint UNSIGNED NOT NULL,
  `name_city` varchar(255) COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cities`
--

INSERT INTO `cities` (`id`, `name_city`) VALUES
(1, 'Thành phố Hà Nội'),
(2, 'Tỉnh Hà Giang'),
(4, 'Tỉnh Cao Bằng'),
(6, 'Tỉnh Bắc Kạn'),
(8, 'Tỉnh Tuyên Quang'),
(10, 'Tỉnh Lào Cai'),
(11, 'Tỉnh Điện Biên'),
(12, 'Tỉnh Lai Châu'),
(14, 'Tỉnh Sơn La'),
(15, 'Tỉnh Yên Bái'),
(17, 'Tỉnh Hòa Bình'),
(19, 'Tỉnh Thái Nguyên'),
(20, 'Tỉnh Lạng Sơn'),
(22, 'Tỉnh Quảng Ninh'),
(24, 'Tỉnh Bắc Giang'),
(25, 'Tỉnh Phú Thọ'),
(26, 'Tỉnh Vĩnh Phúc'),
(27, 'Tỉnh Bắc Ninh'),
(30, 'Tỉnh Hải Dương'),
(31, 'Thành phố Hải Phòng'),
(33, 'Tỉnh Hưng Yên'),
(34, 'Tỉnh Thái Bình'),
(35, 'Tỉnh Hà Nam'),
(36, 'Tỉnh Nam Định'),
(37, 'Tỉnh Ninh Bình'),
(38, 'Tỉnh Thanh Hóa'),
(40, 'Tỉnh Nghệ An'),
(42, 'Tỉnh Hà Tĩnh'),
(44, 'Tỉnh Quảng Bình'),
(45, 'Tỉnh Quảng Trị'),
(46, 'Tỉnh Thừa Thiên Huế'),
(48, 'Thành phố Đà Nẵng'),
(49, 'Tỉnh Quảng Nam'),
(51, 'Tỉnh Quảng Ngãi'),
(52, 'Tỉnh Bình Định'),
(54, 'Tỉnh Phú Yên'),
(56, 'Tỉnh Khánh Hòa'),
(58, 'Tỉnh Ninh Thuận'),
(60, 'Tỉnh Bình Thuận'),
(62, 'Tỉnh Kon Tum'),
(64, 'Tỉnh Gia Lai'),
(66, 'Tỉnh Đắk Lắk'),
(67, 'Tỉnh Đắk Nông'),
(68, 'Tỉnh Lâm Đồng'),
(70, 'Tỉnh Bình Phước'),
(72, 'Tỉnh Tây Ninh'),
(74, 'Tỉnh Bình Dương'),
(75, 'Tỉnh Đồng Nai'),
(77, 'Tỉnh Bà Rịa - Vũng Tàu'),
(79, 'Thành phố Hồ Chí Minh'),
(80, 'Tỉnh Long An'),
(82, 'Tỉnh Tiền Giang'),
(83, 'Tỉnh Bến Tre'),
(84, 'Tỉnh Trà Vinh'),
(86, 'Tỉnh Vĩnh Long'),
(87, 'Tỉnh Đồng Tháp'),
(89, 'Tỉnh An Giang'),
(91, 'Tỉnh Kiên Giang'),
(92, 'Thành phố Cần Thơ'),
(93, 'Tỉnh Hậu Giang'),
(94, 'Tỉnh Sóc Trăng'),
(95, 'Tỉnh Bạc Liêu'),
(96, 'Tỉnh Cà Mau');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `like_major_details`
--

CREATE TABLE `like_major_details` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `major_id` bigint UNSIGNED NOT NULL,
  `date_like` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `like_major_details`
--

INSERT INTO `like_major_details` (`id`, `user_id`, `major_id`, `date_like`) VALUES
(12, 3, 11, '2025-06-01 15:33:30'),
(13, 4, 9, '2025-06-07 11:13:12');

-- --------------------------------------------------------

--
-- Table structure for table `majors`
--

CREATE TABLE `majors` (
  `id` bigint UNSIGNED NOT NULL,
  `name_major` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `major_code` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Mã ngành',
  `image_major` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `description_major` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `like_major` int NOT NULL DEFAULT '0',
  `view_major` int NOT NULL DEFAULT '0',
  `content_major` longtext COLLATE utf8mb4_general_ci NOT NULL,
  `author_id` bigint UNSIGNED NOT NULL,
  `category_major_id` bigint UNSIGNED DEFAULT NULL,
  `date_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status_major` tinyint NOT NULL DEFAULT '0',
  `admission_score` decimal(4,2) DEFAULT NULL COMMENT 'Điểm chuẩn năm gần nhất',
  `admission_method` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Phương thức xét tuyển',
  `admission_quota` int DEFAULT NULL COMMENT 'Chỉ tiêu tuyển sinh',
  `total_credits` int DEFAULT NULL COMMENT 'Tổng số tín chỉ',
  `training_duration` decimal(3,1) DEFAULT NULL COMMENT 'Thời gian đào tạo (năm)',
  `degree_level` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Bậc đào tạo (Cử nhân, Kỹ sư, ...)',
  `training_mode` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Hình thức đào tạo (Chính quy, Liên thông, ...)',
  `career_opportunities` text COLLATE utf8mb4_general_ci COMMENT 'Cơ hội nghề nghiệp',
  `average_salary_range` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Mức lương trung bình',
  `job_positions` text COLLATE utf8mb4_general_ci COMMENT 'Các vị trí công việc',
  `contact_email` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Email liên hệ khoa',
  `contact_phone` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Số điện thoại liên hệ',
  `office_address` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Địa chỉ văn phòng khoa',
  `website_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Website của khoa',
  `gallery_images` json DEFAULT NULL COMMENT 'Thư viện ảnh (JSON array)',
  `video_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Video giới thiệu',
  `brochure_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Link tài liệu brochure',
  `special_requirements` text COLLATE utf8mb4_general_ci COMMENT 'Yêu cầu đặc biệt',
  `facilities` text COLLATE utf8mb4_general_ci COMMENT 'Cơ sở vật chất',
  `notable_achievements` text COLLATE utf8mb4_general_ci COMMENT 'Thành tích nổi bật',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Ngành học nổi bật',
  `priority_order` int NOT NULL DEFAULT '0' COMMENT 'Thứ tự ưu tiên hiển thị',
  `introduction` text COLLATE utf8mb4_general_ci COMMENT 'Giới thiệu ngành học',
  `job_opportunities` text COLLATE utf8mb4_general_ci COMMENT 'Cơ hội việc làm',
  `post_graduation_opportunities` text COLLATE utf8mb4_general_ci COMMENT 'Cơ hội sau đại học',
  `contact_info` text COLLATE utf8mb4_general_ci COMMENT 'Thông tin liên hệ'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `majors`
--

INSERT INTO `majors` (`id`, `name_major`, `major_code`, `image_major`, `description_major`, `like_major`, `view_major`, `content_major`, `author_id`, `category_major_id`, `date_updated`, `status_major`, `admission_score`, `admission_method`, `admission_quota`, `total_credits`, `training_duration`, `degree_level`, `training_mode`, `career_opportunities`, `average_salary_range`, `job_positions`, `contact_email`, `contact_phone`, `office_address`, `website_url`, `gallery_images`, `video_url`, `brochure_url`, `special_requirements`, `facilities`, `notable_achievements`, `is_featured`, `priority_order`, `introduction`, `job_opportunities`, `post_graduation_opportunities`, `contact_info`) VALUES
(7, 'Kinh tế', NULL, '/images/images.jpeg', 'Đào tạo sinh viên kiến thức về quản lý, tài chính, kinh doanh và phân tích thị trường, giúp sinh viên phát triển khả năng lãnh đạo và tư duy chiến lược.', 0, 23, 'abc', 2, 1, '2025-06-16 09:17:43', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(8, 'Luật', NULL, '0', 'Cung cấp kiến thức về pháp lý, hệ thống pháp luật, bảo vệ quyền lợi hợp pháp của cá nhân và tổ chức, chuẩn bị cho sinh viên làm việc trong các lĩnh vực pháp luật, tư vấn và tranh tụng.', 0, 8, 'abc', 2, 2, '2025-06-16 08:26:47', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(9, 'Ngôn ngữ', NULL, '0', 'Đào tạo sinh viên về ngôn ngữ và văn hóa, bao gồm các kỹ năng giao tiếp, biên dịch, phiên dịch và nghiên cứu ngôn ngữ, giúp sinh viên làm việc trong môi trường quốc tế.', 2, 3, 'abc', 2, 3, '2025-06-16 17:01:25', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(10, 'Văn hóa', NULL, '0', 'Tập trung vào việc nghiên cứu và phát triển các giá trị văn hóa, di sản văn hóa dân tộc, giúp sinh viên hiểu rõ về văn hóa trong xã hội và bảo tồn, phát huy các giá trị truyền thống.', 0, 5, 'abc', 2, 3, '2025-06-12 09:17:06', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(11, 'Nghệ thuật', NULL, '0', 'Chuyên sâu về các lĩnh vực nghệ thuật như âm nhạc, mỹ thuật, múa và thiết kế, giúp sinh viên phát triển khả năng sáng tạo và thẩm mỹ trong nghệ thuật biểu diễn và ứng dụng.', 1, 22, 'abc', 2, 3, '2025-06-07 07:42:43', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(12, 'Dược', NULL, '0', 'Đào tạo chuyên sâu về các lĩnh vực y tế và chăm sóc sức khỏe, từ việc phòng ngừa đến điều trị bệnh tật, giúp sinh viên có thể làm việc trong môi trường y tế, bệnh viện và các cơ sở', 0, 1, 'abc', 2, 4, '2025-06-16 06:44:39', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(13, 'Thể thao đó', NULL, NULL, 'd', 0, 0, '<p>d</p>', 3, NULL, '2025-06-01 10:47:04', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(14, 'Kế Toán', 'KT001', NULL, 'Đây là mô tả ngắn', 0, 59, '<p>dsvdfsbfdnbfgnbfgngn</p>', 2, 1, '2025-06-16 11:05:51', 0, NULL, NULL, 120, NULL, NULL, 'Cử nhân', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 'bfbfgh', 'gfnfgnfg', 'fgnfgnfg', 'fgnfgnfgn'),
(15, 'Luật', NULL, NULL, 'dsfvdsvfdbv', 0, 92, '<p>fdbfdbfdbfdbfdbdfb</p>', 2, 1, '2025-06-16 17:14:01', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(16, 'Quản trị kinh doanh', NULL, NULL, 'dgvdsgdrfhhthth', 0, 127, '<p>trhtrhfghfgjfgjfgjhj</p>', 2, 1, '2025-06-16 08:27:02', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(17, 'Tài chính – Ngân hàng', NULL, NULL, 'dvfdhjykltherdgrhgdh', 1, 97, '<p>hfghfgjhfgjfgjmhgmghm</p>', 2, 1, '2025-06-16 08:27:06', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL),
(18, 'TEST', 'test mã ngành', NULL, 'test mô tả ngắn', 0, 3, '<p>test ndct</p>', 2, 2, '2025-06-16 11:53:57', 0, NULL, NULL, 120, NULL, NULL, 'test danh hiệu', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 'test ngabnhf học', 'co hoi viec lam', 'ấccaca', 'lien hệ');

-- --------------------------------------------------------

--
-- Table structure for table `major_admission_methods`
--

CREATE TABLE `major_admission_methods` (
  `id` bigint UNSIGNED NOT NULL,
  `major_id` bigint UNSIGNED NOT NULL,
  `admission_method_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `major_admission_methods`
--

INSERT INTO `major_admission_methods` (`id`, `major_id`, `admission_method_id`, `created_at`, `updated_at`) VALUES
(2, 14, 1, '2025-06-16 04:00:23', '2025-06-16 04:00:23'),
(4, 18, 1, '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(5, 18, 2, '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(6, 18, 3, '2025-06-16 04:48:35', '2025-06-16 04:48:35'),
(7, 18, 4, '2025-06-16 04:48:35', '2025-06-16 04:48:35');

-- --------------------------------------------------------

--
-- Table structure for table `major_subject_combinations`
--

CREATE TABLE `major_subject_combinations` (
  `id` bigint UNSIGNED NOT NULL,
  `major_id` bigint UNSIGNED NOT NULL,
  `subject_combination_id` bigint UNSIGNED NOT NULL,
  `min_score` decimal(4,2) DEFAULT NULL COMMENT 'Điểm chuẩn tối thiểu cho tổ hợp này',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_06_16_000001_add_detailed_fields_to_majors_table', 2),
(6, '2025_06_16_000003_remove_subject_combination_from_majors', 3),
(7, '2025_06_16_000005_create_admission_methods_system', 3),
(8, '2025_06_16_000006_fix_description_major_nullable', 4);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`email`, `token`, `created_at`) VALUES
('<EMAIL>', 'mLLophpJ38y9zU1tbLTaY20xGtWH6zlTZ0c7FGtE7ykyth0PRMskvqOP6lus', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `question_request`
--

CREATE TABLE `question_request` (
  `id` bigint UNSIGNED NOT NULL,
  `name_request` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `phone_request` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `email_request` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `birth` date NOT NULL,
  `city_id` bigint UNSIGNED NOT NULL,
  `major_id` bigint UNSIGNED NOT NULL,
  `school` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `status_request` tinyint NOT NULL DEFAULT '0' COMMENT '0 is not process,1 processed',
  `date_request` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `question_request`
--

INSERT INTO `question_request` (`id`, `name_request`, `phone_request`, `email_request`, `birth`, `city_id`, `major_id`, `school`, `status_request`, `date_request`) VALUES
(1, 'Nguyễn Vũ Đại Nam', '0365245602', '<EMAIL>', '2025-05-27', 31, 7, 'Thăng Long', 0, '2025-06-01 11:08:46'),
(2, 'Nguyễn Vũ Đại Nam', '0365245602', '<EMAIL>', '2025-05-02', 1, 7, 'Thăng Long', 0, '2025-06-01 11:08:46'),
(3, 'Nguyễn Vũ Đại Nam', '0365245602', '<EMAIL>', '2025-05-16', 31, 8, 'Thăng Long', 0, '2025-06-01 11:08:46'),
(4, 'Nguyễn Vũ Đại Nam', '0365245602', '<EMAIL>', '2025-05-23', 31, 9, 'Thăng Long', 1, '2025-06-01 11:08:46'),
(5, 'Nguyễn Vũ Đại Nam', '0365245602', '<EMAIL>', '2025-05-23', 31, 9, 'Thăng Long', 1, '2025-06-01 11:08:46'),
(6, 'Nguyễn Vũ Đại Nam', '0365245602', '<EMAIL>', '2025-05-23', 31, 9, 'Thăng Long', 1, '2025-06-01 11:08:46'),
(7, 'Tín Lê', '0949062229', '<EMAIL>', '2003-06-10', 84, 11, 'THPT Phạm Thái Bường', 1, '2025-06-12 10:35:33'),
(8, 'Nguyễn Văn An', '0912345678', '<EMAIL>', '2006-04-15', 79, 7, 'THPT Lê Hồng Phong', 1, '2025-06-03 02:21:15'),
(9, 'Trần Thị Bình', '0987654321', '<EMAIL>', '2005-11-07', 1, 14, 'THPT Trưng Vương', 1, '2025-06-10 07:15:33'),
(10, 'Lê Quốc Cường', '0901122334', '<EMAIL>', '2006-02-20', 48, 15, 'THPT Nguyễn Huệ', 1, '2025-06-17 00:12:45'),
(11, 'Phạm Thu Duyên', '0978112233', '<EMAIL>', '2005-09-10', 31, 16, 'THPT Bùi Thị Xuân', 1, '2025-06-25 04:33:50'),
(12, 'Hoàng Văn Em', '0933445566', '<EMAIL>', '2006-01-05', 92, 17, 'THPT Nguyễn Du', 1, '2025-06-07 06:24:18'),
(13, 'Đặng Thị Giang', '0967888999', '<EMAIL>', '2005-07-25', 60, 7, 'THPT Lê Quý Đôn', 1, '2025-06-11 09:55:10'),
(14, 'Ngô Minh Hòa', '0955667788', '<EMAIL>', '2006-06-18', 22, 14, 'THPT Trần Phú', 1, '2025-06-02 01:05:22'),
(15, 'Võ Hữu Ích', '0944221133', '<EMAIL>', '2005-09-09', 45, 15, 'THPT Chuyên Hùng Vương', 1, '2025-06-19 07:42:37'),
(16, 'Lý Ánh Khoa', '0922334455', '<EMAIL>', '2006-03-12', 75, 16, 'THPT Nguyễn Trãi', 1, '2025-06-22 10:23:48'),
(17, 'Tạ Mỹ Linh', '0911778899', '<EMAIL>', '2005-05-28', 83, 17, 'THPT Phan Đình Phùng', 1, '2025-06-28 05:14:29'),
(18, 'Bùi Quốc Minh', '0977001122', '<EMAIL>', '2006-08-01', 2, 7, 'THPT Nguyễn Thị Minh Khai', 1, '2025-06-14 02:09:09'),
(19, 'Đỗ Thị Ngọc', '0933998877', '<EMAIL>', '2005-10-19', 36, 14, 'THPT Marie Curie', 1, '2025-06-06 03:34:56'),
(20, 'Trịnh Văn Nam', '0988111222', '<EMAIL>', '2006-01-30', 6, 15, 'THPT Trần Hưng Đạo', 1, '2025-06-05 08:00:00'),
(21, 'Phan Kim Oanh', '0955887766', '<EMAIL>', '2005-03-22', 87, 16, 'THPT Nguyễn Bỉnh Khiêm', 1, '2025-06-20 11:15:12'),
(22, 'Huỳnh Tấn Phát', '0944556677', '<EMAIL>', '2006-11-11', 17, 17, 'THPT Nguyễn Công Trứ', 1, '2025-06-18 06:13:13'),
(23, 'Cao Thị Quỳnh', '0911223344', '<EMAIL>', '2005-08-16', 56, 7, 'THPT Gia Định', 1, '2025-06-13 03:10:10'),
(24, 'Đinh Văn Sơn', '0909988776', '<EMAIL>', '2006-10-08', 38, 14, 'THPT Nguyễn Chí Thanh', 1, '2025-06-04 00:07:07'),
(25, 'Kiều Mai Trang', '0988997766', '<EMAIL>', '2005-06-27', 64, 15, 'THPT Thăng Long', 1, '2025-06-01 01:30:00'),
(26, 'Trương Nhật Uyên', '0922113344', '<EMAIL>', '2006-12-12', 24, 16, 'THPT Chu Văn An', 1, '2025-06-16 07:14:14'),
(27, 'Lâm Tuấn Vũ', '0966554433', '<EMAIL>', '2005-01-17', 14, 17, 'THPT Hùng Vương', 1, '2025-06-08 02:45:00');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('qqYMeRzsLzapUdR29UqXKf7jPTJBkNsg1sGtD5r5', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiUVRscGxkQWVXd3dzSTJReVJVeUNNdUtPVTVseXl1clJqbkFjc0RySiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1750074837),
('SqkQmJ0sAPto4MtXIHomkPd9TnP6fwxwAyywjb8a', 2, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiZjVGSExSQ2VSeDdWbEZpcU1jV2FOSnE4QUR5SVo1YTUxT3o2ZjV1TCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9hZG1pbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjI7fQ==', 1750074919),
('z28lJPdYdCRxjbVzW7bRnkXQSCrFy2KcOSMXDqF6', 2, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiV3hYaG81dDdUUXFid1N3TVNJYnkwTE5MV0Z3YlBJSmdvUG04aG9PNCI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTIxOiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvYWRtaW4vcmVwb3J0P190b2tlbj1XeFhobzV0N1RRcWJ3U3dNU0lieTBMTkxXRndiUElKZ29QbThob080JmNhdGVnb3J5X21ham9yX2lkPTEmcXVhcnRlcj0meWVhcj0yMDI1Ijt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6Mjt9', 1750094043);

-- --------------------------------------------------------

--
-- Table structure for table `subject_combinations`
--

CREATE TABLE `subject_combinations` (
  `id` bigint UNSIGNED NOT NULL,
  `code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mã tổ hợp (A00, A01, D01, ...)',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tên tổ hợp môn',
  `subjects` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Các môn học trong tổ hợp',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'Mô tả chi tiết',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Trạng thái hoạt động',
  `priority_order` int NOT NULL DEFAULT '0' COMMENT 'Thứ tự ưu tiên',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subject_combinations`
--

INSERT INTO `subject_combinations` (`id`, `code`, `name`, `subjects`, `description`, `is_active`, `priority_order`, `created_at`, `updated_at`) VALUES
(1, 'A01', 'Toán - Lý - Anh', 'Toán học, Vật lý, Tiếng anh', NULL, 1, 0, '2025-06-16 02:45:05', '2025-06-16 03:34:39'),
(2, 'A00', 'Toán - Lý - Hóa', 'Toán học, Vật lý, Hóa học', NULL, 1, 0, '2025-06-16 03:34:31', '2025-06-16 03:34:31');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `age` int NOT NULL,
  `role` tinyint NOT NULL DEFAULT '0' COMMENT '0 is user, 1 is admin',
  `status_user` tinyint NOT NULL DEFAULT '0',
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `address`, `phone`, `age`, `role`, `status_user`, `remember_token`, `created_at`, `updated_at`) VALUES
(2, 'admin', '<EMAIL>', NULL, '$2y$12$VIqifTAjJvPcnaVPFmVnS.sM3g007cIGnWZ6Se4nMOT6i7CAeHlme', 'Hà Nội', '0365245602', 20, 1, 0, NULL, '2025-06-11 11:01:08', NULL),
(3, 'Nguyễn Vũ Đại Nam', '<EMAIL>', NULL, '$2y$12$sc7P16alIwSdUUv3JzJnB.tRVCwDypHfmsiGFMY0pQnnyBYvMSasO', 'Bình Thạnh', '0365245602', 23, 0, 0, NULL, '2025-06-03 11:01:12', NULL),
(4, 'Lê Trực Tín', '<EMAIL>', NULL, '$2y$12$ktSmgPJH7zQIOVuqemw3lOTicGUROcAAsSADP8JAkBFRFMDgcA2f2', 'Trà Vinh', '0949062229', 18, 0, 0, NULL, '2025-06-02 07:24:08', NULL),
(5, 'Nguyễn Thiện Nhân', '<EMAIL>', NULL, '$2y$12$iX1BDH3iYQD0k4M0MwHfDezp0GpbXkAHDqqPEK1Kwe4hCuCq3y2he', 'Bến Tre', '0333276346', 22, 0, 1, NULL, '2025-06-05 15:11:37', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admission_methods`
--
ALTER TABLE `admission_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admission_methods_code_unique` (`code`);

--
-- Indexes for table `admission_scores`
--
ALTER TABLE `admission_scores`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admission_score_unique` (`major_id`,`admission_method_id`,`subject_combination_id`,`year`),
  ADD KEY `admission_scores_admission_method_id_foreign` (`admission_method_id`),
  ADD KEY `admission_scores_subject_combination_id_foreign` (`subject_combination_id`);

--
-- Indexes for table `blogs`
--
ALTER TABLE `blogs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_author_id_blogs_to_users` (`author_id`),
  ADD KEY `fk_category_blog_id_blogs_to_category_blog` (`category_blog_id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `category_blog`
--
ALTER TABLE `category_blog`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `category_major`
--
ALTER TABLE `category_major`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `chat_ai`
--
ALTER TABLE `chat_ai`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_user_id_chat_ai_to_users` (`user_id`);

--
-- Indexes for table `cities`
--
ALTER TABLE `cities`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `like_major_details`
--
ALTER TABLE `like_major_details`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_user_id_like_to_users` (`user_id`),
  ADD KEY `fk_major_id_like_to_majors` (`major_id`);

--
-- Indexes for table `majors`
--
ALTER TABLE `majors`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_author_id_majors_to_users` (`author_id`),
  ADD KEY `fk_category_major_id_majors_to_category_major` (`category_major_id`);

--
-- Indexes for table `major_admission_methods`
--
ALTER TABLE `major_admission_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `major_admission_method_unique` (`major_id`,`admission_method_id`),
  ADD KEY `major_admission_methods_admission_method_id_foreign` (`admission_method_id`);

--
-- Indexes for table `major_subject_combinations`
--
ALTER TABLE `major_subject_combinations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `major_subject_combinations_major_id_foreign` (`major_id`),
  ADD KEY `major_subject_combinations_subject_combination_id_foreign` (`subject_combination_id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `question_request`
--
ALTER TABLE `question_request`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_city_id_question_request_to_cities` (`city_id`),
  ADD KEY `fk_major_id_question_request_to_major` (`major_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `subject_combinations`
--
ALTER TABLE `subject_combinations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subject_combinations_code_unique` (`code`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admission_methods`
--
ALTER TABLE `admission_methods`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `admission_scores`
--
ALTER TABLE `admission_scores`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT for table `blogs`
--
ALTER TABLE `blogs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `category_blog`
--
ALTER TABLE `category_blog`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `category_major`
--
ALTER TABLE `category_major`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `chat_ai`
--
ALTER TABLE `chat_ai`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `cities`
--
ALTER TABLE `cities`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=97;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `like_major_details`
--
ALTER TABLE `like_major_details`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `majors`
--
ALTER TABLE `majors`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `major_admission_methods`
--
ALTER TABLE `major_admission_methods`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `major_subject_combinations`
--
ALTER TABLE `major_subject_combinations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `question_request`
--
ALTER TABLE `question_request`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `subject_combinations`
--
ALTER TABLE `subject_combinations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admission_scores`
--
ALTER TABLE `admission_scores`
  ADD CONSTRAINT `admission_scores_admission_method_id_foreign` FOREIGN KEY (`admission_method_id`) REFERENCES `admission_methods` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admission_scores_major_id_foreign` FOREIGN KEY (`major_id`) REFERENCES `majors` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admission_scores_subject_combination_id_foreign` FOREIGN KEY (`subject_combination_id`) REFERENCES `subject_combinations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `blogs`
--
ALTER TABLE `blogs`
  ADD CONSTRAINT `fk_author_id_blogs_to_users` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `fk_category_blog_id_blogs_to_category_blog` FOREIGN KEY (`category_blog_id`) REFERENCES `category_blog` (`id`);

--
-- Constraints for table `chat_ai`
--
ALTER TABLE `chat_ai`
  ADD CONSTRAINT `fk_user_id_chat_ai_to_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `like_major_details`
--
ALTER TABLE `like_major_details`
  ADD CONSTRAINT `fk_major_id_like_to_majors` FOREIGN KEY (`major_id`) REFERENCES `majors` (`id`),
  ADD CONSTRAINT `fk_user_id_like_to_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `majors`
--
ALTER TABLE `majors`
  ADD CONSTRAINT `fk_author_id_majors_to_users` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `fk_category_major_id_majors_to_category_major` FOREIGN KEY (`category_major_id`) REFERENCES `category_major` (`id`);

--
-- Constraints for table `major_admission_methods`
--
ALTER TABLE `major_admission_methods`
  ADD CONSTRAINT `major_admission_methods_admission_method_id_foreign` FOREIGN KEY (`admission_method_id`) REFERENCES `admission_methods` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `major_admission_methods_major_id_foreign` FOREIGN KEY (`major_id`) REFERENCES `majors` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `major_subject_combinations`
--
ALTER TABLE `major_subject_combinations`
  ADD CONSTRAINT `major_subject_combinations_major_id_foreign` FOREIGN KEY (`major_id`) REFERENCES `majors` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `major_subject_combinations_subject_combination_id_foreign` FOREIGN KEY (`subject_combination_id`) REFERENCES `subject_combinations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `question_request`
--
ALTER TABLE `question_request`
  ADD CONSTRAINT `fk_city_id_question_request_to_cities` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`),
  ADD CONSTRAINT `fk_major_id_question_request_to_major` FOREIGN KEY (`major_id`) REFERENCES `majors` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
