@extends('user.layout')
@section('title', 'Trang chủ')
@section('content')
<title>Danh sách bài viết</title>
<style>
  .filter-form {
      display: flex;
      align-items: center;
      gap: 10px;
    }
</style>
<section id="mu-from-blog">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <div class="mu-from-blog-area">
            <!-- start title -->
            <div class="mu-title">
              <h2>Danh sách bài viết</h2>
              <p>Khám phá những bài viết, sáng tác và cảm nhận từ sinh viên, giảng viên và cộng đồng TVU. Các bài viết này phản ánh những khoảnh khắc đáng nhớ, tình cảm gắn bó với mái trườ<PERSON>, cũng như những đóng góp tích cực của TVU trong gi<PERSON><PERSON> dụ<PERSON>, nghiên cứu và phục vụ cộng đồng. <PERSON><PERSON><PERSON> là nơi lưu giữ những câu chuyện truyền cảm hứng, kỷ niệm đáng nhớ và những sáng tác nghệ thuật đặc sắc, góp phần khẳng định giá trị văn hóa và sứ mệnh của trường</p>
            </div>
            <!-- end title -->  
            <!-- start from blog content   -->
            <div class="mu-from-blog-content">
              <div class="row">
                <form class="filter-form" action="#" method="POST">
                  @csrf
    <select class="form-control" name="category_blog_id" id="filter">
      <option value="0" selected>Tất cả danh mục</option>
      @foreach ($dataBlogCategory as $item)
      <option value="{{$item->id}}">{{$item->name_category_blog}}</option>
      @endforeach
      <!-- Bạn thêm các option ở đây -->
      <!-- <option value="1">Giá trị 1</option> -->
    </select>
    <button class="btn btn-primary" type="button" onclick="filterMajor(this)">Lọc</button>
  </form>
              </div>
              <div class="row" id="blogList">
                @foreach ( $dataBlog as $item )
                <div class="col-md-4 col-sm-4 " style="margin-top: 50px;">
                  <article class="mu-blog-single-item">
                    <figure class="mu-blog-single-img">
                      <a href="#"><img width="100%" height="300px" src="{{$item->image_blog}}" alt="img"></a>
                      <figcaption class="mu-blog-caption">
                        <h3><a href="#">{{$item->name_blog}}</a></h3>
                      </figcaption>                      
                    </figure>
                    <div class="mu-blog-meta">
                      <a href="#">Author: {{$item->author_name}}</a>
                      <a href="#">{{$item->date_blog}}</a>
                      <span>Lượt xem: {{$item->view_blog}}</span>
                    </div>
                    <div class="mu-blog-meta">
                      <a href="#">Chủ đề: {{$item->name_category_blog}}</a>
                    </div>
                    <div class="mu-blog-description">
                      <p>{{$item->description_blog}}</p>
                      <a class="mu-read-more-btn" href="{{ route('user.blogDetail', ['id' => $item->id]) }}">Đọc thêm</a>
                    </div>
                  </article>
                </div>
                @endforeach
              </div>
            </div>     
            <!-- end from blog content   -->   
          </div>
        </div>
      </div>
    </div>
  </section>
  <script>
    function filterMajor(button){
      const parent = button.closest('.filter-form');

    // 2. Tìm thẻ select trong thẻ cha
    const select = parent.querySelector('select');

    // 3. Lấy giá trị được chọn
    const value = select.value;
 let csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      fetch('/blog-category/'+value, {  
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
        },
    })
    .then(response => response.json())  
    .then(data => {
      document.querySelector('#blogList').innerHTML = '';
      
      data.dataBlog.forEach(element => {
        document.querySelector('#blogList').innerHTML +=`
        <div class="col-md-4 col-sm-4 " style="margin-top: 50px;">
                  <article class="mu-blog-single-item">
                    <figure class="mu-blog-single-img">
                      <a href="#"><img width="100%" height="300px" src="${element.image_blog}" alt="img"></a>
                      <figcaption class="mu-blog-caption">
                        <h3><a href="#">${element.name_blog}</a></h3>
                      </figcaption>                      
                    </figure>
                    <div class="mu-blog-meta">
                      <a href="#">Author: ${element.author_name}</a>
                      <a href="#">${element.date_blog}</a>
                      <span>Lượt xem: ${element.view_blog}</span>
                    </div>
                    <div class="mu-blog-meta">
                      <a href="#">Chủ đề: ${element.name_category_blog}</a>
                    </div>
                    <div class="mu-blog-description">
                      <p>${element.description_blog}</p>
                      <a class="mu-read-more-btn" href="/blog-detail/${element.id}">Đọc thêm</a>
                    </div>
                  </article>
                </div>
        `
      });
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById("response").innerText = 'Có lỗi xảy ra!';
    });
    }
  </script>
@endsection
