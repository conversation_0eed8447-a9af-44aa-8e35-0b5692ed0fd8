@extends('user.layout')
@section('title', 'Trang chủ')
@section('content')
  <style>
    body {
      background-color: #f7f7f7;
      padding: 30px 0;
    }
    .profile-box {
      background: #fff;
      width: 80%;
      margin-top: 100px;
      margin-bottom: 100px;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h3 {
      margin-bottom: 20px;
      text-align: center;
    }
    .avatar-preview {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 10px;
    }
    .form-group.required label:after {
      content: "*";
      color: red;
      margin-left: 5px;
    }
  </style>
<div class="container" style="display: flex; justify-content: center;">
  <div class="profile-box">
    <h3>Cập nhật thông tin cá nhân</h3>
    <form id="profileForm" action="" method="POST">
      @csrf
      <!-- Tên -->
      <div class="form-group required">
        <label for="name">Họ và tên</label>
        <input type="text" class="form-control" name="name" id="name" placeholder="Nhập họ tên" value="{{Auth::user()->name}}" required>
      </div>

      <!-- Email -->
      <div class="form-group required">
        <label for="email">Email</label>
        <input type="email" class="form-control" name="email" id="email" value="{{Auth::user()->email}}" placeholder="Nhập email" required>
      </div>

      <!-- Số điện thoại -->
      <div class="form-group required">
        <label for="phone">Số điện thoại</label>
        <input type="text" class="form-control" name="phone" id="phone" maxlength="10" pattern="[0-9]{10}" value="{{Auth::user()->phone}}" placeholder="Nhập số điện thoại" required>
      </div>

      <div class="form-group required">
        <label for="address">Địa chỉ</label>
        <input type="text" class="form-control" name="address" id="address" value="{{Auth::user()->address}}" placeholder="Nhập địa chỉ" required>
      </div>

      <div class="form-group required">
        <label for="age">Tuổi</label>
        <input type="number" class="form-control" name="age" id="age" min="1" max="100" step="1" value="{{Auth::user()->age}}" placeholder="Nhập số tuổi" required>
      </div>

      <!-- Nút lưu -->
      <button type="submit" class="btn btn-primary btn-block">Lưu thay đổi</button>
    </form>
  </div>
</div>
@endsection
