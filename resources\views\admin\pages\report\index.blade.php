@extends('admin.layout')
@section('content')
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
<div class="container py-4">
    <h3>📊 Thống kê tổng quan</h3>


    <h5 class="mt-4">🔥 Top 5 ngành học được quan tâm nhiều nhất</h5>
     <table class="table ">
        <thead>
            <tr>
                <th>Thứ hạng</th>
                <th>Tên ngành</th>
                <th>L<PERSON><PERSON><PERSON> thích</th>
                <th>Lượt xem</th>
            </tr>
        </thead>
        <tbody>
            @foreach($mostPopularMajors as $index => $item)
                <tr>
                    <td>{{++$index}}</td>
                    <td>{{ $item->name_major }}</td>
                    <td>{{ number_format($item->like_major) }}</td>
                    <td>{{ number_format($item->view_major) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    <h6><PERSON><PERSON> lọc</h6>
<form method="GET" action="" class="row g-3 align-items-end mb-4">
    @csrf
  <div class="col-auto">
    <label for="year" class="form-label">Năm</label>
    <select class="form-control border p-2" id="year" name="year">
      @for ($y = date('Y'); $y >= 2020; $y--)
        <option value="{{ $y }}" {{ request('year') == $y ? 'selected' : '' }}>{{ $y }}</option>
      @endfor
    </select>
  </div>

  <div class="col-auto">
    <label for="quarter" class="form-label">Quý</label>
    <select class="form-control border p-2" id="quarter" name="quarter">
      <option value="">-- Tất cả --</option>
      <option value="1" {{ request('quarter') == 1 ? 'selected' : '' }}>Quý 1 (1-3)</option>
      <option value="2" {{ request('quarter') == 2 ? 'selected' : '' }}>Quý 2 (4-6)</option>
      <option value="3" {{ request('quarter') == 3 ? 'selected' : '' }}>Quý 3 (7-9)</option>
      <option value="4" {{ request('quarter') == 4 ? 'selected' : '' }}>Quý 4 (10-12)</option>
    </select>
  </div>
  <div class="col-auto">
    <label for="quarter" class="form-label">Khối ngành</label>
    <select class="form-control border p-2" name="category_major_id" id="filter" required>
      @foreach ($dataMajorCategory as $item)
      <option value="{{$item->id}}" {{ request('category_major_id') == $item->id ? 'selected' : '' }}>{{$item->name_category_major}}</option>
      @endforeach
    </select>
  </div>
  
  <div class="">
    <button type="submit" class="btn btn-primary" style="width:5%">Lọc</button>
  </div>
</form>
<h5 class="mt-4">🔥 BXH ngành học được quan tâm nhiều nhất theo khối ngành đã lọc</h5>
<div class="row">
        <div class="col-lg-12 col-md-12 mt-4 mb-4">
          <div class="card">
            <div class="card-body">
              <h6 class="mb-0 ">Thứ tự quan tâm theo khối ngành</h6>
              <p class="text-sm "></p>
              <div class="pe-2">
                <div class="chart">
                  <canvas id="chart-bars" class="chart-canvas" height="170"></canvas>
                </div>
              </div>
              <hr class="dark horizontal">
              <div class="d-flex ">
              </div>
            </div>
          </div>
        </div>
      </div>
     <table class="table ">
        <thead>
            <tr>
                <th>Thứ hạng</th>
                <th>Tên ngành</th>
                <th>Lượt thích</th>
                <th>Lượt câu hỏi</th>
                <th>Lượt xem</th>
                <th>Các thí sinh quan tâm đến từ</th>
            </tr>
        </thead>
        <tbody>
            @foreach($mostPopularMajorsFilter as $index => $item)
                <tr>
                    <td>{{++$index}}</td>
                    <td>{{ $item->name_major }}</td>
                    <td>{{ number_format($item->total_like) }}</td>
                    <td>{{ number_format($item->total_request) }}</td>
                    <td>{{ number_format($item->view_major) }}</td>
                    <td>{{ $item->related_cities }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    
</div>
</main>
@section('scripts')
<script>
    var ctx = document.getElementById("chart-bars").getContext("2d");
 const labels = {!! json_encode($label) !!};
 const view_major = {!! json_encode($view_major) !!};
    new Chart(ctx, {
      type: "bar",
      data: {
        labels: labels,
        datasets: [{
          label: "Views",
          tension: 0.4,
          borderWidth: 0,
          borderRadius: 4,
          borderSkipped: false,
          backgroundColor: "#43A047",
          data: view_major,
          barThickness: 'flex'
        }, ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false,
          }
        },
        interaction: {
          intersect: false,
          mode: 'index',
        },
        scales: {
          y: {
            grid: {
              drawBorder: false,
              display: true,
              drawOnChartArea: true,
              drawTicks: false,
              borderDash: [5, 5],
              color: '#e5e5e5'
            },
            ticks: {
              suggestedMin: 0,
              suggestedMax: 500,
              beginAtZero: true,
              padding: 10,
              font: {
                size: 14,
                lineHeight: 2
              },
              color: "#737373"
            },
          },
          x: {
            grid: {
              drawBorder: false,
              display: false,
              drawOnChartArea: false,
              drawTicks: false,
              borderDash: [5, 5]
            },
            ticks: {
              display: true,
              color: '#737373',
              padding: 10,
              font: {
                size: 14,
                lineHeight: 2
              },
            }
          },
        },
      },
    });
</script>
@endsection
@endsection
