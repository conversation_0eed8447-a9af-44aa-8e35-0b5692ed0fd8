-- Tạo bảng subject_combinations
CREATE TABLE IF NOT EXISTS `subject_combinations` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` varchar(10) NOT NULL COMMENT '<PERSON><PERSON> tổ hợp (A00, A01, D01, ...)',
  `name` varchar(255) NOT NULL COMMENT 'Tên tổ hợp môn',
  `subjects` text NOT NULL COMMENT '<PERSON><PERSON><PERSON> môn học trong tổ hợp',
  `description` text NULL COMMENT 'Mô tả chi tiết',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Trạng thái hoạt động',
  `priority_order` int NOT NULL DEFAULT '0' COMMENT 'Thứ tự ưu tiên',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_combinations_code_unique` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tạo bảng major_subject_combinations
CREATE TABLE IF NOT EXISTS `major_subject_combinations` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `major_id` bigint UNSIGNED NOT NULL,
  `subject_combination_id` bigint UNSIGNED NOT NULL,
  `min_score` decimal(4,2) NULL COMMENT 'Điểm chuẩn tối thiểu cho tổ hợp này',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `major_subject_combination_unique` (`major_id`, `subject_combination_id`),
  KEY `major_subject_combinations_major_id_foreign` (`major_id`),
  KEY `major_subject_combinations_subject_combination_id_foreign` (`subject_combination_id`),
  CONSTRAINT `major_subject_combinations_major_id_foreign` FOREIGN KEY (`major_id`) REFERENCES `majors` (`id`) ON DELETE CASCADE,
  CONSTRAINT `major_subject_combinations_subject_combination_id_foreign` FOREIGN KEY (`subject_combination_id`) REFERENCES `subject_combinations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Thêm dữ liệu mẫu
INSERT INTO `subject_combinations` (`code`, `name`, `subjects`, `description`, `is_active`, `priority_order`, `created_at`, `updated_at`) VALUES
('A00', 'Toán - Lý - Hóa', 'Toán học, Vật lý, Hóa học', 'Tổ hợp dành cho các ngành kỹ thuật, công nghệ', 1, 1, NOW(), NOW()),
('A01', 'Toán - Lý - Tiếng Anh', 'Toán học, Vật lý, Tiếng Anh', 'Tổ hợp dành cho các ngành kỹ thuật có yêu cầu ngoại ngữ', 1, 2, NOW(), NOW()),
('D01', 'Toán - Văn - Tiếng Anh', 'Toán học, Ngữ văn, Tiếng Anh', 'Tổ hợp dành cho các ngành kinh tế, quản trị', 1, 3, NOW(), NOW()),
('C00', 'Văn - Sử - Địa', 'Ngữ văn, Lịch sử, Địa lý', 'Tổ hợp dành cho các ngành xã hội, nhân văn', 1, 4, NOW(), NOW()),
('B00', 'Toán - Hóa - Sinh', 'Toán học, Hóa học, Sinh học', 'Tổ hợp dành cho các ngành y dược, sinh học', 1, 5, NOW(), NOW()),
('D07', 'Toán - Hóa - Tiếng Anh', 'Toán học, Hóa học, Tiếng Anh', 'Tổ hợp dành cho các ngành có yêu cầu hóa học và ngoại ngữ', 1, 6, NOW(), NOW());
