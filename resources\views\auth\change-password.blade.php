@extends('user.layout')
@section('title', 'Trang chủ')
@section('content')
  <style>
    body {
      background: #f0f2f5;
    }
    .change-password-box {
      width: 100%;
      background: #fff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .change-password-box .container{
        margin-top: 100px;
    }
    .change-password-box h4 {
      text-align: center;
      margin-bottom: 20px;
    }
  </style>
    <div class="change-password-box">
        <div class="container">
        <h4><PERSON><PERSON><PERSON> mật khẩu</h4>
        <form id="changePasswordForm" action="" method="POST">
          @csrf
          <div class="form-group">
            <label for="currentPassword">M<PERSON>t khẩu hiện tại</label>
            <input type="password" class="form-control" name="old_password" id="currentPassword" required>
          </div>
          <div class="form-group">
            <label for="newPassword"><PERSON><PERSON><PERSON> kh<PERSON>u mới</label>
            <input type="password" class="form-control" name="password" id="newPassword" required minlength="8">
          </div>
          <div class="form-group">
            <label for="confirmPassword">Xác nhận mật khẩu mới</label>
            <input type="password" class="form-control" name="confirmPassword" id="confirmPassword" required minlength="8">
          </div>
          <button type="submit" class="btn btn-primary btn-block">Xác nhận</button>
        </form>
        @if (session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger">
        {{ session('error') }}
    </div>
@endif
    </div>
      </div>
@endsection
