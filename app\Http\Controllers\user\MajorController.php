<?php

namespace App\Http\Controllers\user;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MajorController extends Controller
{
    public function reactMajor($id){
        DB::table('like_major_details')->insert([
                'user_id'         => Auth::user()->id,
                'major_id' => $id,
            ]);
            DB::table('majors')->where('id', $id)->update([
                'like_major' => DB::raw('like_major + 1'),
            ]);
            $likeMajor = DB::table('majors')
        ->select('like_major')
        ->where('id', $id)
        ->first();
        $data = json_decode($likeMajor->like_major, true);
        return response()->json([
                'like_major' => $data
            ]);
    }
    public function unReactMajor($id){
        DB::table('like_major_details')
        ->where('major_id', $id)
        ->where('user_id', Auth::user()->id)
        ->delete();
            DB::table('majors')->where('id', $id)->update([
                'like_major' => DB::raw('like_major - 1'),
            ]);
            $likeMajor = DB::table('majors')
        ->select('like_major')
        ->where('id', $id)
        ->first();
        $data = json_decode($likeMajor->like_major, true);
        return response()->json([
                'like_major' => $data
            ]);
    }
    public function checkLike($id){
         $checkLike = DB::table('like_major_details')
        ->select('*')
        ->where('user_id', Auth::user()->id)
        ->where('major_id', $id)
        ->first();
        if($checkLike){
           return response()->json([
                'check' => true
            ]);
        }
           return response()->json([
                'check' => false
            ]);
    }
}
