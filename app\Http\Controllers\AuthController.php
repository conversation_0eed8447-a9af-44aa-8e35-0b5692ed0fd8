<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        return view('auth.auth');
    }
    public function changeInfor()
    {
        return view('auth.infor');
    }
    public function update(Request $request)
    {
        $request->validate([
            'name'     => 'required|string|max:255',
            'email'    => 'required|email|unique:users,email,' . Auth::user()->id,
            'address'  => 'required|string|max:255',
            'phone'    => 'required|string|max:10',
            'age'      => 'required|integer|min:0',
        ]);

        DB::table('users')->where('id', Auth::user()->id)->update([
            'name'              => $request->name,
            'email'             => $request->email,
            'address'           => $request->address,
            'phone'             => $request->phone,
            'age'               => $request->age,
        ]);
        Auth::loginUsingId(Auth::user()->id);
        return back();
    }
    public function changePassword()
    {
        return view('auth.change-password');
    }
    public function changePassword_(Request $request)
    {
        $user = DB::table('users')->where('id', Auth::user()->id)->first();

        if ($user && Hash::check($request->old_password, $user->password) && $request->password == $request->confirmPassword) {
            DB::table('users')->where('id', Auth::user()->id)->update([
                'password' => Hash::make($request->password),
            ]);
            return back()->with('success', 'Đổi mật khẩu thành công!');
        }
        return back()->with('error', 'Đổi mật khẩu thất bại!');
    }

    public function register(Request $request)
    {
        $request->validate([
            'name'     => 'required|string|max:255',
            'email'    => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
            'address'  => 'required|string|max:255',
            'phone'    => 'required|string|max:10',
            'age'      => 'required|integer|min:0',
        ]);

        $userId = DB::table('users')->insertGetId([
            'name'              => $request->name,
            'email'             => $request->email,
            'password'          => Hash::make($request->password),
            'address'           => $request->address,
            'phone'             => $request->phone,
            'age'               => $request->age,
            'role'              => 0,
        ]);
        Auth::loginUsingId($userId);
        return redirect()->route('user.home')->with('success', 'Đăng ký thành công. Vui lòng đăng nhập.');
    }

    public function login(Request $request)
    {
        $credentials = $request->only('email', 'password');

        $user = DB::table('users')->where('email', $credentials['email'])->first();

        if ($user && Hash::check($credentials['password'], $user->password)) {
            Auth::loginUsingId($user->id);
            if (Auth::user()->role == 0) {
                return redirect()->route('user.home');
            } else if (Auth::user()->role == 1) {
                return redirect()->route('admin.home');
            }
        }

        return back()->withErrors(['email' => 'Email hoặc mật khẩu không đúng']);
    }
    public function resetPassword($token)
    {
        $checkToken = DB::table('password_reset_tokens')
            ->select('*')
            ->where('token', $token)
            ->first();
        if ($checkToken) {
            return view('auth.reset-password');
        } else {
            return redirect()->route('user.auth');
        }
    }
    public function resetPassword_(Request $request, $token)
    {
        $checkToken = DB::table('password_reset_tokens')
            ->select('*')
            ->where('token', $token)
            ->first();
        if ($checkToken && $request->password == $request->confirmPassword) {
            DB::table('users')->where('email', $checkToken->email)->update([
                'password' => Hash::make($request->password),
            ]);
            return redirect()->route('user.auth');
        } else {
            return back()->with('error', 'Đổi mật khẩu thất bại!');
        }
    }

    public function logout()
    {
        Auth::logout();
        return redirect()->route('user.auth');
    }
}
