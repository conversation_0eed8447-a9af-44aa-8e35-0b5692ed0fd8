<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\user\HomeUserController;
use App\Http\Controllers\user\ChatController;
use App\Http\Controllers\user\MajorController;
use App\Http\Controllers\EmailController;

use App\Http\Controllers\admin\HomeAdminController;
use App\Http\Controllers\admin\BlogAdminController;
use App\Http\Controllers\admin\UserAdminController;
use App\Http\Controllers\admin\ChatAdminController;
use App\Http\Controllers\admin\MajorAdminController;
use App\Http\Controllers\admin\MajorCategoryController;
use App\Http\Controllers\admin\SubjectCombinationController;
use App\Http\Controllers\admin\QuestionAdminController;
use App\Http\Controllers\admin\ReportAdminController;

use App\Http\Middleware\checkRoleAdmin;
use App\Http\Middleware\checkRoleUser;
use App\Http\Middleware\RedirectIfAuthenticated;
use App\Http\Middleware\RedirectIfNotAuthenticated;
use App\Http\Middleware\CorsMiddleware;

Route::get('home', [HomeUserController::class, 'index'])->name('user.home');
Route::get('/', [HomeUserController::class, 'index'])->name('user.home1');
Route::get('/major-list', [HomeUserController::class, 'majorList'])->name('user.majorList');
Route::get('/major-detail/{id}', [HomeUserController::class, 'majorDetail'])->name('user.majorDetail');
Route::get('/blog-list', [HomeUserController::class, 'blogList'])->name('user.blogList');
Route::get('/blog-detail/{id}', [HomeUserController::class, 'blogDetail'])->name('user.blogDetail');
Route::post('/question-request', [QuestionController::class, 'create'])->name('user.questionRequest');
Route::get('/change-infor', [AuthController::class, 'changeInfor'])->name('user.changeInfor');
Route::post('/change-infor', [AuthController::class, 'update']);
Route::get('/change-password', [AuthController::class, 'changePassword'])->name('user.changePassword');
Route::post('/change-password', [AuthController::class, 'changePassword_']);
Route::get('/major-category/{id}', [MajorAdminController::class, 'getMajorByIdCategory']);

// API routes for subject combinations
Route::get('/api/subject-combinations/active', [SubjectCombinationController::class, 'getActiveSubjectCombinations']);
Route::get('/api/major/{id}/subject-combinations', [SubjectCombinationController::class, 'getMajorSubjectCombinations']);

// API routes for admission methods
Route::get('/api/admission-methods/active', [MajorAdminController::class, 'getActiveAdmissionMethods']);
Route::get('/api/major/{id}/admission-methods', [MajorAdminController::class, 'getMajorAdmissionMethods']);
Route::get('/api/major/{id}/admission-scores', [MajorAdminController::class, 'getMajorAdmissionScores']);
Route::get('/blog-category/{id}', [BlogAdminController::class, 'getBlogByIdCategory']);

//only member access
Route::middleware([checkRoleUser::class])->group(function () {
Route::get('/check-like/{id}', [MajorController::class, 'checkLike']);
Route::get('/react-major/{id}', [MajorController::class, 'reactMajor']);
Route::get('/unreact-major/{id}', [MajorController::class, 'unReactMajor']);
Route::get('/ai-chat', [HomeUserController::class, 'aiChat'])->name('user.aiChat');
Route::get('/load-message', [ChatController::class, 'loadMessage']);
Route::middleware([CorsMiddleware::class])->post('/chat', [ChatController::class, 'chat']);
});


    //cant access after login
Route::middleware([RedirectIfAuthenticated::class])->group(function () {
Route::get('auth', [AuthController::class, 'showLoginForm'])->name('user.auth');
Route::post('login', [AuthController::class, 'login'])->name('login');
Route::post('email-reset', [EmailController::class, 'sendMailResetPass'])->name('user.sendMailResetPass');
Route::get('/reset-password/{token}', [AuthController::class, 'resetPassword'])->name('auth.reset-password');
Route::post('/reset-password/{token}', [AuthController::class, 'resetPassword_']);
Route::post('register', [AuthController::class, 'register'])->name('register');
});

//only access after login
Route::post('/logout', [authController::class, 'logout'])->middleware([RedirectIfNotAuthenticated::class])->name('logout');

//only admin access
Route::prefix('admin')->middleware([checkRoleAdmin::class])->group(function () {
Route::get('/', [HomeAdminController::class, 'index'])->name('admin.home');
Route::get('/blog', [BlogAdminController::class, 'index'])->name('admin.blog');
Route::get('/user', [UserAdminController::class, 'index'])->name('admin.user');
Route::get('/chat', [ChatAdminController::class, 'index'])->name('admin.chat');
Route::get('/question', [QuestionAdminController::class, 'index'])->name('admin.question');
Route::get('/major', [MajorAdminController::class, 'index'])->name('admin.major');
Route::get('/major-category', [MajorCategoryController::class, 'index'])->name('admin.majorCategory');
Route::get('/subject-combination', [SubjectCombinationController::class, 'index'])->name('admin.subjectCombination');
Route::get('/report', [ReportAdminController::class, 'index'])->name('admin.report');

Route::get('/blog/{id}', [BlogAdminController::class, 'updateView'])->name('admin.blog.updateView');
Route::post('/blog', [BlogAdminController::class, 'createBlog']);
Route::put('/blog/{id}', [BlogAdminController::class, 'updateBlog']);
Route::delete('/blog/{id}', [BlogAdminController::class, 'softDeleteBlog']);
Route::patch('/blog/restore/{id}', [BlogAdminController::class, 'restoreBlog']);

Route::get('/major/{id}', [MajorAdminController::class, 'updateView'])->name('admin.major.updateView');
Route::post('/major', [MajorAdminController::class, 'createMajor']);
Route::put('/major/{id}', [MajorAdminController::class, 'updateMajor'])->name('admin.major.update');
Route::delete('/major/{id}', [MajorAdminController::class, 'softDeleteMajor']);
Route::patch('/major/restore/{id}', [MajorAdminController::class, 'restoreMajor']);

Route::get('/major-category/{id}', [MajorCategoryController::class, 'updateView'])->name('admin.majorCategory.updateView');
Route::post('/major-category', [MajorCategoryController::class, 'createMajorCategory']);
Route::put('/major-category/{id}', [MajorCategoryController::class, 'updateMajorCategory']);
Route::delete('/major-category/{id}', [MajorCategoryController::class, 'softDeleteMajorCategory']);
Route::patch('/major-category/restore/{id}', [MajorCategoryController::class, 'restoreMajorCategory']);

// Subject Combination routes
Route::post('/subject-combination', [SubjectCombinationController::class, 'create'])->name('admin.subjectCombination.create');
Route::get('/subject-combination/{id}', [SubjectCombinationController::class, 'updateView'])->name('admin.subjectCombination.updateView');
Route::put('/subject-combination/{id}', [SubjectCombinationController::class, 'update'])->name('admin.subjectCombination.update');
Route::patch('/subject-combination/{id}/toggle', [SubjectCombinationController::class, 'toggleStatus'])->name('admin.subjectCombination.toggleStatus');
Route::delete('/subject-combination/{id}', [SubjectCombinationController::class, 'delete'])->name('admin.subjectCombination.delete');

Route::delete('/users/{id}', [UserAdminController::class, 'softDeleteUser'])->name('admin.user.softDelete');
Route::patch('/users/restore/{id}', [UserAdminController::class, 'restoreUser'])->name('admin.user.restore');

Route::delete('/questions/{id}', [QuestionAdminController::class, 'softDeleteQuestion'])->name('admin.question.softDelete');
Route::patch('/questions/restore/{id}', [QuestionAdminController::class, 'restoreQuestion'])->name('admin.question.restore');

Route::get('/chat/{id}', [ChatAdminController::class, 'detailView'])->name('admin.chat.detailView');
Route::get('/load-message-admin/{id}', [ChatController::class, 'loadMessageAdmin']);
});
