<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MajorCategoryController extends Controller
{
     public function index()
    {
            $dataMajorCategory = DB::table('category_major')
        ->select('*')
        ->get();

        return view('admin.pages.majorCategory.index', compact('dataMajorCategory'));
    }

    public function createMajorCategory(Request $request)
    {

        DB::table('category_major')->insert([
            'name_category_major' => $request->name_category_major,
        ]);

        return back();
    }

    public function updateView($id)
    {
 $dataMajorCategory = DB::table('category_major')
        ->select('*')
        ->where('id', $id)
        ->first();
        return view('admin.pages.majorCategory.update', compact('dataMajorCategory'));
    }

    public function updateMajorCategory(Request $request, $id)
    {
        DB::table('category_major')
            ->where('id', $id)
            ->update([
                'name_category_major' => $request->name_category_major,
            ]);

        return redirect()->route('admin.majorCategory');
    }

    public function softDeleteMajorCategory($id)
    {
        DB::table('category_major')
            ->where('id', $id)
            ->update(['status_category_major' => 1]);

        return back();
    }

    public function restoreMajorCategory($id)
    {
        DB::table('category_major')
            ->where('id', $id)
            ->update(['status_category_major' => 0]);

        return back();
    }
}
