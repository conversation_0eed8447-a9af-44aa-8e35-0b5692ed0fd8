
// border radius

@mixin border-radius($radius){
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms--border-radius: $radius;
    border-radius: $radius;
}

// transition

@mixin transition($args) {
  -webkit-transition: $args;
  -moz-transition: $args;
  -ms-transition: $args;
  -o-transition: $args;
  transition: $args;
}

// opacity

@mixin opacity($opacity) {
  opacity: $opacity;
  $opacity-ie: $opacity * 100;
  filter: alpha(opacity=$opacity-ie); //IE8
}

// placeholder

@mixin placeholder{
  &::-webkit-input-placeholder {@content}
  &:-moz-placeholder           {@content}
  &::-moz-placeholder          {@content}
  &:-ms-input-placeholder      {@content}  
}

//====usages=====
// @include placeholder {
//     font-style:italic;
//     color: white;
//     font-weight:100;
// }

// =====transform==============

// generic transform
@mixin transform($transforms) {
	   -moz-transform: $transforms;
	     -o-transform: $transforms;
	    -ms-transform: $transforms;
	-webkit-transform: $transforms;
          transform: $transforms;
}
// rotate
@mixin rotate ($deg) {
  @include transform(rotate(#{$deg}deg));
}
 
// scale
@mixin scale($scale) {
	 @include transform(scale($scale));
} 
// translate
@mixin translate ($x, $y) {
   @include transform(translate($x, $y));
}
// skew
@mixin skew ($x, $y) {
   @include transform(skew(#{$x}deg, #{$y}deg));
}
//transform origin
@mixin transform-origin ($origin) {
    moz-transform-origin: $origin;
	     -o-transform-origin: $origin;
	    -ms-transform-origin: $origin;
	-webkit-transform-origin: $origin;
          transform-origin: $origin;
}

// 	usages
//  .test {
//   @include skew(25,10);
//   @include transform-origin(top left);
//   @include rotate(60);
//   @include scale(2);
//   @include translate(5,5);    
// }

// box-shadow

@mixin box-shadow($top, $left, $blur, $color, $inset:"") {

      -webkit-box-shadow:$top $left $blur $color #{$inset};
      -moz-box-shadow:$top $left $blur $color #{$inset};
      box-shadow:$top $left $blur $color #{$inset};
    }
// usages    
// .test{
// 	@include box-shadow(inset, 0px, 1px, 1px, rgba(0, 0, 0, 0.5));
// }    

