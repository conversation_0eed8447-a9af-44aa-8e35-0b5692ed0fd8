@extends('user.layout')
@section('title', 'Trang chủ')
@section('content')
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: #f4f4f4;
      height: 100vh;
    }
    .chat-container {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }

    .message {
      max-width: 70%;
      margin-bottom: 10px;
      padding: 10px 15px;
      border-radius: 15px;
      clear: both;
    }

    .message.user {
      background-color: #d1e7dd;
      color: #000;
      float: right;
      text-align: right;
      border-bottom-right-radius: 0;
    }

    .message.gpt {
      background-color: #ffffff;
      color: #000;
      float: left;
      border-bottom-left-radius: 0;
      border: 1px solid #ddd;
    }

    .input-container {
      display: flex;
      padding: 10px;
      background: #fff;
      border-top: 1px solid #ccc;
      margin-bottom: 100px;
    }

    .input-container input {
      flex: 1;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
      font-size: 16px;
    }

    .input-container button {
      margin-left: 10px;
      padding: 10px 20px;
      font-size: 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }

    .input-container button:hover {
      background-color: #0056b3;
    }
    .ai-title{
        margin-top: 200px;
    }
  </style>
<div class="container">
    <h1 class="ai-title" style="font-weight: bold;">AI TƯ VẤN NGÀNH HỌC</h1>
    <div class="chat-container" id="chat">
      </div>
    
      <div class="input-container">
        <input type="text" id="user-message" placeholder="Nhập tin nhắn..." />
        <button onclick="sendMessage()">Gửi</button>
      </div>
</div>
  <script>
    function loadMessage(){
       const chat = document.getElementById("chat");
            let csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
fetch('/load-message', {  
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
        },
    })
    .then(response => response.json())  
    .then(data => {
      data.dataMessage.forEach(element => {
        if(element.type_message==0){
      const userMsg = document.createElement("div");
      userMsg.className = "message user";
      userMsg.textContent = element.message_content;
      chat.appendChild(userMsg);
        }else{
        const gptMsg = document.createElement("div");
        gptMsg.className = "message gpt";
        gptMsg.textContent = element.message_content;
        chat.appendChild(gptMsg);
        chat.scrollTop = chat.scrollHeight;
        }
      });
      const gptMsg = document.createElement("div");
        gptMsg.className = "message gpt";
        gptMsg.textContent = "Xin chào! Tôi có thể giúp gì cho bạn hôm nay?";
        chat.appendChild(gptMsg);
        chat.scrollTop = chat.scrollHeight;
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById("response").innerText = 'Có lỗi xảy ra!';
    });
}
loadMessage()

    function sendMessage() {
      const chat = document.getElementById("chat");
            let csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    const userMessage = document.getElementById("user-message").value;
    const message = userMessage.trim();
    const userMsg = document.createElement("div");
      userMsg.className = "message user";
      userMsg.textContent = message;
      chat.appendChild(userMsg);

      // Reset input
      document.getElementById("user-message").value = "";

      // Auto scroll
      chat.scrollTop = chat.scrollHeight;
    fetch('/chat', {  
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
        },
        body: JSON.stringify({ content: userMessage })
    })
    .then(response => response.json())  
    .then(data => {
      const gptMsg = document.createElement("div");
        gptMsg.className = "message gpt";
        gptMsg.textContent = data.message;
        chat.appendChild(gptMsg);
        chat.scrollTop = chat.scrollHeight;
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById("response").innerText = 'Có lỗi xảy ra!';
    });
}

    // function sendMessage() {
    //   const input = document.getElementById("userInput");
    //   const message = input.value.trim();

    //   if (message === "") return;

    //   // Hiển thị tin nhắn người dùng
      

    //   // Giả lập phản hồi GPT (sau này gọi API ở đây)
    //   setTimeout(() => {
        
    //   }, 1000);
    // }
  </script>

@endsection