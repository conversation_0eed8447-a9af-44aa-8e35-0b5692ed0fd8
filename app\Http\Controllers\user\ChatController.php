<?php

namespace App\Http\Controllers\user;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ChatController extends Controller
{
    public function chat(Request $request)
    {
        // Tạo đối tượng Client của Guzzle
        $client = new Client();

        // Cấu hình dữ liệu JSON gửi đến API
        $data = [
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'Tôi là chuyên gia tư vấn ngành học cho sinh viên Đại học Trà Vinh. Tôi chỉ trả lời các câu hỏi liên quan đến ngành học, dựa trên sở thích, đam mê môn học hoặc đặc điểm phù hợp với từng ngành. Các câu hỏi ngoài phạm vi này, vui lòng hỏi lại cho đúng nội dung ngành học nhé! Bạn hãy nhớ nhắc lại rằng những ngành học này Đại Học Trà Vinh đang đào tạo nha'
                ],
                [
                    'role' => 'user',
                    'content' => $request->input('content') 
                ]
            ]
        ];

        // Gửi yêu cầu POST tới OpenAI API
        try {
            $response = $client->post('https://api.openai.com/v1/chat/completions', [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Bearer ********************************************************************************************************************************************************************',
                    'Content-Type' => 'application/json'
                ]
            ]);

            // Lấy dữ liệu trả về và xử lý
            $body = $response->getBody();
            $data = json_decode($body, true);

            DB::table('chat_ai')->insert([
                'user_id'         => Auth::user()->id,
                'message_content' => $request->input('content'),
            ]);
            DB::table('chat_ai')->insert([
                'user_id'         => Auth::user()->id,
                'message_content' => $data['choices'][0]['message']['content'],
                'type_message'    => 1,
            ]);

            return response()->json([
                'message' => $data['choices'][0]['message']['content']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error occurred while communicating with OpenAI API',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function loadMessage(){
         $dataMessage = DB::table('chat_ai')
        ->select('*')
        ->where('user_id', Auth::user()->id)
        ->orderBy('id','ASC')
        ->get();
        $data = json_decode($dataMessage, true);
        return response()->json([
                'dataMessage' => $data
            ]);
    }
    public function loadMessageAdmin($id){
         $dataMessage = DB::table('chat_ai')
        ->select('*')
        ->where('user_id', $id)
        ->orderBy('id','ASC')
        ->get();
        $data = json_decode($dataMessage, true);
        return response()->json([
                'dataMessage' => $data
            ]);
    }
}
