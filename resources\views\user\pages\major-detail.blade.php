@extends('user.layout')
@section('title', '<PERSON> tiết ngành {{$dataMajor->name_major}}')
@section('content')
<title>Chi tiết ngành {{$dataMajor->name_major}}</title>
<style>
    body {
        font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #f8f9fa;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }

    .mu-main-content,
    .main-content,
    section {
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        max-width: 100% !important;
        width: 100% !important;
    }

    .container-detail {
        background: #fff;
        padding: 40px 20px;
        margin: 0;
        max-width: 100%;
        width: 100vw;
        min-height: calc(100vh - 200px);
        box-shadow: none;
    }

    .major-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .major-header h1 {
        font-size: 2.8em;
        margin: 0 0 10px 0;
        font-weight: 700;
    }

    .major-header .update-date {
        font-size: 1em;
        opacity: 0.9;
        margin: 0;
    }

    .major-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .info-card {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .info-card .label {
        font-size: 0.9em;
        color: #6c757d;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .info-card .value {
        font-size: 1.4em;
        font-weight: 700;
        color: #2c3e50;
    }

    .info-card.primary .value {
        color: #007bff;
    }

    .info-card.success .value {
        color: #28a745;
    }

    .admission-methods-section {
        margin: 40px 0;
        background: #fff;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .section-title {
        font-size: 1.8em;
        color: #2c3e50;
        margin-bottom: 25px;
        font-weight: 700;
        border-left: 4px solid #007bff;
        padding-left: 15px;
    }

    .admission-method {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }

    .admission-method::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background: linear-gradient(45deg, #007bff, #0056b3);
    }

    .method-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .method-name {
        font-size: 1.3em;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }

    .score-display {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .current-score {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 700;
        font-size: 1.1em;
    }

    .chart-btn {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .chart-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }

    .method-description {
        color: #6c757d;
        margin: 10px 0;
        line-height: 1.6;
    }

    .subject-combinations {
        margin-top: 15px;
    }

    .combinations-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
    }

    .combination-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .combination-tag {
        background: linear-gradient(45deg, #e9ecef, #f8f9fa);
        border: 1px solid #dee2e6;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.9em;
        color: #495057;
    }

    .combination-tag .code {
        font-weight: 700;
        color: #007bff;
    }

    .stats-section {
        display: flex;
        justify-content: center;
        gap: 60px;
        margin: 30px 0;
        flex-wrap: wrap;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2.2em;
        font-weight: 700;
        color: #dc3545;
        display: block;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9em;
        margin-top: 5px;
    }

    .content-section {
        background: #fff;
        border-radius: 15px;
        padding: 30px;
        margin: 30px 0;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .content-section h2 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-weight: 700;
        border-left: 4px solid #28a745;
        padding-left: 15px;
    }

    .content-section p,
    .content-section div {
        line-height: 1.8;
        color: #495057;
        font-size: 1.05em;
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background-color: #fff;
        margin: 5% auto;
        padding: 0;
        border-radius: 15px;
        width: 90%;
        max-width: 800px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from { transform: translateY(-50px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 30px;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.4em;
        font-weight: 700;
    }

    .close {
        color: white;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: opacity 0.3s ease;
    }

    .close:hover {
        opacity: 0.7;
    }

    .modal-body {
        padding: 30px;
    }

    .chart-container {
        width: 100%;
        height: 400px;
        margin-bottom: 20px;
    }

    .chart-info {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 20px;
        border-radius: 8px;
    }

    .chart-info h4 {
        color: #007bff;
        margin: 0 0 10px 0;
        font-weight: 700;
    }

    .chart-info p {
        margin: 0;
        line-height: 1.6;
        color: #495057;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .container-detail {
            padding: 20px 15px;
        }

        .major-header h1 {
            font-size: 2.2em;
        }

        .major-info-grid {
            grid-template-columns: 1fr;
        }

        .method-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .stats-section {
            gap: 30px;
        }

        .modal-content {
            width: 95%;
            margin: 10% auto;
        }

        .modal-body {
            padding: 20px;
        }
    }

    @media (max-width: 480px) {
        .major-header {
            padding: 20px 15px;
        }

        .major-header h1 {
            font-size: 1.8em;
        }

        .admission-methods-section,
        .content-section {
            padding: 20px;
        }
    }
</style>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="container-detail">
    <div class="container">
        <!-- Header Section -->
        <div class="major-header">
            <h1>{{$dataMajor->name_major}}</h1>
            <p class="update-date">Cập nhật lần cuối {{$dataMajor->date_updated}}</p>
        </div>

        <!-- Major Info Grid -->
        <div class="major-info-grid">
            @if($dataMajor->major_code)
            <div class="info-card primary">
                <div class="label">Mã ngành:</div>
                <div class="value">{{$dataMajor->major_code}}</div>
            </div>
            @endif

            @if($dataMajor->admission_quota)
            <div class="info-card success">
                <div class="label">Chỉ tiêu:</div>
                <div class="value">{{$dataMajor->admission_quota}} sinh viên</div>
            </div>
            @endif

            @if($dataMajor->training_duration)
            <div class="info-card">
                <div class="label">Thời gian đào tạo:</div>
                <div class="value">{{$dataMajor->training_duration}} năm</div>
            </div>
            @endif

            @if($dataMajor->degree_level)
            <div class="info-card">
                <div class="label">Danh hiệu cấp bằng:</div>
                <div class="value">{{$dataMajor->degree_level}}</div>
            </div>
            @endif
        </div>

        <!-- Description Section -->
        @if($dataMajor->description_major)
        <div class="content-section">
            <h2>📖 Mô tả ngành học</h2>
            <p>{{$dataMajor->description_major}}</p>

            <!-- Like and View Stats -->
            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-number">{{$dataMajor->like_major}}</span>
                    <div class="stat-label">Lượt yêu thích</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" style="color: #007bff;">{{$dataMajor->view_major}}</span>
                    <div class="stat-label">Lượt truy cập</div>
                </div>
            </div>

            <!-- Like Button -->
            <div style="text-align: center; margin-top: 20px;">
                @if (Auth::check())
                <button id="btn-like" onclick="toggleLike(this)" style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fa-regular fa-heart" style="margin-right: 8px;"></i>
                    Yêu thích ngành học
                </button>
                @else
                <button disabled style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: not-allowed; font-weight: 600;">
                    <i class="fa-regular fa-heart" style="margin-right: 8px;"></i>
                    Yêu thích ngành học
                </button>
                <p style="margin-top: 10px;"><a href="{{route('user.auth')}}" style="color: #007bff;">Đăng nhập để tương tác!</a></p>
                @endif
            </div>
        </div>
        @endif
        <!-- Admission Methods Section -->
        @if(isset($admissionMethods) && count($admissionMethods) > 0)
        <div class="admission-methods-section">
            <h2 class="section-title">Phương thức xét tuyển</h2>

            @foreach($admissionMethods as $method)
            <div class="admission-method">
                <div class="method-header">
                    <h3 class="method-name">{{ $method->name }}</h3>
                    <div class="score-display">
                        @if(isset($admissionScores[$method->id]))
                        <div class="current-score">
                            Điểm chuẩn 2025: {{ $admissionScores[$method->id] }}
                        </div>
                        <button class="chart-btn" onclick="showChart('{{ $method->name }}', {{ $method->id }})">
                            <i class="fas fa-chart-line"></i>
                            Biểu đồ so sánh điểm chuẩn
                        </button>
                        @endif
                    </div>
                </div>

                <p class="method-description">{{ $method->description }}</p>

                @if($method->requires_subject_combinations && isset($subjectCombinations) && count($subjectCombinations) > 0)
                <div class="subject-combinations">
                    <div class="combinations-label">Tổ hợp môn xét tuyển:</div>
                    <div class="combination-tags">
                        @foreach($subjectCombinations as $combination)
                        <div class="combination-tag">
                            <span class="code">{{ $combination->code }}</span> - {{ $combination->name }}
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Content Sections -->
        @if($dataMajor->content_major)
        <div class="content-section">
            <h2>📖 Giới thiệu ngành học</h2>
            <div>{!! $dataMajor->content_major !!}</div>
        </div>
        @endif

        @if($dataMajor->job_opportunities)
        <div class="content-section">
            <h2>💼 Cơ hội việc làm</h2>
            <div>{!! $dataMajor->job_opportunities !!}</div>
        </div>
        @endif

        @if($dataMajor->post_graduation_opportunities)
        <div class="content-section">
            <h2>🎓 Cơ hội sau đại học</h2>
            <div>{!! $dataMajor->post_graduation_opportunities !!}</div>
        </div>
        @endif

        @if($dataMajor->contact_info)
        <div class="content-section">
            <h2>� Thông tin liên hệ</h2>
            <div>{!! $dataMajor->contact_info !!}</div>
        </div>
        @endif

    <!-- Cơ sở vật chất và thành tích -->
    @if($dataMajor->facilities || $dataMajor->notable_achievements)
    <div class="section">
      <h2>🏢 Cơ sở vật chất & Thành tích</h2>
      @if($dataMajor->facilities)
        <div>
          <strong>Cơ sở vật chất:</strong>
          <p>{{ $dataMajor->facilities }}</p>
        </div>
      @endif
      @if($dataMajor->notable_achievements)
        <div>
          <strong>Thành tích nổi bật:</strong>
          <p>{{ $dataMajor->notable_achievements }}</p>
        </div>
      @endif
    </div>
    @endif

    <!-- Yêu cầu đặc biệt -->
    @if($dataMajor->special_requirements)
    <div class="section">
      <h2>⚠️ Yêu cầu đặc biệt</h2>
      <p>{{ $dataMajor->special_requirements }}</p>
    </div>
    @endif

    <!-- Video giới thiệu -->
    @if($dataMajor->video_url)
    <div class="section">
      <h2>🎥 Video giới thiệu</h2>
      <div class="video-container" style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
        <iframe src="{{ $dataMajor->video_url }}"
                style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"
                frameborder="0" allowfullscreen>
        </iframe>
      </div>
    </div>
    @endif

    <!-- Thông tin liên hệ -->
    @if($dataMajor->contact_email || $dataMajor->contact_phone || $dataMajor->office_address || $dataMajor->website_url)
    <div class="section">
      <h2>📞 Thông tin liên hệ</h2>
      @if($dataMajor->contact_email)
        <p><strong>Email:</strong> <a href="mailto:{{ $dataMajor->contact_email }}">{{ $dataMajor->contact_email }}</a></p>
      @endif
      @if($dataMajor->contact_phone)
        <p><strong>Điện thoại:</strong> <a href="tel:{{ $dataMajor->contact_phone }}">{{ $dataMajor->contact_phone }}</a></p>
      @endif
      @if($dataMajor->office_address)
        <p><strong>Địa chỉ:</strong> {{ $dataMajor->office_address }}</p>
      @endif
      @if($dataMajor->website_url)
        <p><strong>Website:</strong> <a href="{{ $dataMajor->website_url }}" target="_blank">{{ $dataMajor->website_url }}</a></p>
      @endif
      @if($dataMajor->brochure_url)
        <p><strong>Tài liệu:</strong> <a href="{{ $dataMajor->brochure_url }}" target="_blank">Tải brochure</a></p>
      @endif
    </div>
    @endif

    </div>
  </div>
  <script>
    function checkLike(){
       const icon = document.querySelector("#btn-like").querySelector('i');
    const isLiked = icon.classList.contains('fa-solid');
    fetch('/check-like/'+{{$dataMajor->id}}, {
      method: 'GET',
      })
      .then(response => response.json())
      .then(data => {
        if(data.check){
icon.classList.remove('fa-regular');
      icon.classList.add('fa-solid');
        }else{
icon.classList.remove('fa-solid');
      icon.classList.add('fa-regular');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        document.getElementById("response").innerText = 'Có lỗi xảy ra!';
      });
    }
    checkLike()
    function toggleLike(button) {
    let csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    const loveCount = document.querySelector('.love-count');
    const icon = button.querySelector('i');
    const isLiked = icon.classList.contains('fa-solid');

    if (isLiked) {
      icon.classList.remove('fa-solid');
      icon.classList.add('fa-regular');
      fetch('/unreact-major/'+{{$dataMajor->id}}, {
      method: 'GET',
      })
      .then(response => response.json())
      .then(data => {
        loveCount.innerText = data.like_major+' lượt yêu thích'
      })
      .catch(error => {
        console.error('Error:', error);
        document.getElementById("response").innerText = 'Có lỗi xảy ra!';
      });
    } else {
      icon.classList.remove('fa-regular');
      icon.classList.add('fa-solid');
      fetch('/react-major/'+{{$dataMajor->id}}, {
      method: 'GET',
      })
      .then(response => response.json())
      .then(data => {
        loveCount.innerText = data.like_major+' lượt yêu thích'
      })
      .catch(error => {
        console.error('Error:', error);
        document.getElementById("response").innerText = 'Có lỗi xảy ra!';
      });
    }
    }
  </script>
@endsection