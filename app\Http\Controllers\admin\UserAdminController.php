<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserAdminController extends Controller
{
    public function index()
    {
        $dataUsers = DB::table('users')
            ->select('*')
            ->where('role','!=', 1)
            ->orderByDesc('created_at')
            ->get();

        return view('admin.pages.user.index', compact('dataUsers'));
    }


    public function softDeleteUser($id)
    {
        DB::table('users')
            ->where('id', $id)
            ->update(['status_user' => 1]);

        return back()->with('success', 'Xóa user thành công');
    }

    public function restoreUser($id)
    {
        DB::table('users')
            ->where('id', $id)
            ->update(['status_user' => 0]);

        return back()->with('success', '<PERSON><PERSON>c hồi user thành công');
    }
}
