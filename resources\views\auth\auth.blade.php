@extends('user.layout')
@section('title', 'Trang chủ')
@section('content')
<style>
  body {
      background-color: #f5f5f5;
      height: 100vh;
      width: 100%;
    }
    .auth-box {
      background: #fff;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
      width: 100%;
      display: flex;
      justify-content: center;
    }
    .auth-box .container{
      display: flex;
      justify-content: center;
    }
    .auth-box form{
        width: 40%;
    }
    .toggle-link {
      color: #337ab7;
      cursor: pointer;
      text-decoration: underline;
    }
    .has-error .help-block {
      color: red;
    }
</style>
<div class="auth-box">
   <div class="container">
    <form id="loginForm" action="{{route('login')}}" method="POST" novalidate>
      @csrf
        <h4 class="text-center">Đ<PERSON>ng nhập</h4>
        <div class="form-group">
          <label for="loginEmail">Email</label>
          <input type="email" class="form-control" name="email" id="loginEmail" required>
          <span class="help-block" style="display: none;"><PERSON><PERSON> lòng nhập email hợp lệ.</span>
        </div>
        <div class="form-group">
          <label for="loginPassword">Mật khẩu</label>
          <input type="password" class="form-control" name="password" id="loginPassword" required minlength="8">
          <span class="help-block" style="display: none;">Mật khẩu phải có ít nhất 8 ký tự.</span>
        </div>
        <button type="submit" class="btn btn-primary btn-block">Đăng nhập</button>
        <p class="text-center" style="margin-top: 10px;">Chưa có tài khoản? <span class="toggle-link" onclick="switchForm('register')">Đăng ký</span></p>
        <p class="text-center" style="margin-top: 10px;">Quên mật khẩu? <span class="toggle-link" onclick="switchForm('forgot')">Nhấn đây</span></p>
      </form>
    <form id="forgotForm" action="{{route('user.sendMailResetPass')}}" method="POST" class="hidden" novalidate>
  @csrf
  <h4 class="text-center">Quên mật khẩu</h4>
  <div class="form-group">
    <label for="forgotEmail">Email</label>
    <input type="email" class="form-control" name="email" id="forgotEmail" required>
    <span class="help-block" style="display: none;">Vui lòng nhập email hợp lệ.</span>
  </div>
  <button type="submit" class="btn btn-warning btn-block">Gửi liên kết đặt lại mật khẩu</button>
  <p class="text-center" style="margin-top: 10px;">Nhớ mật khẩu? <span class="toggle-link" onclick="switchForm('login')">Đăng nhập</span></p>
</form>

      <form id="registerForm" action="{{route('register')}}" method="POST" class="hidden" novalidate>
        @csrf
        <h4 class="text-center">Đăng ký</h4>
        <div class="form-group">
          <label for="name">Tên</label>
          <input type="text" class="form-control" name="name" id="name" required>
          <span class="help-block" style="display: none;">Vui lòng nhập tên.</span>
        </div>
        <div class="form-group">
          <label for="registerEmail">Email</label>
          <input type="email" class="form-control" name="email" id="registerEmail" required>
          <span class="help-block" style="display: none;">Vui lòng nhập email hợp lệ.</span>
        </div>
        <div class="form-group">
          <label for="registerPassword">Mật khẩu</label>
          <input type="password" class="form-control" name="password" id="registerPassword" minlength="8" required>
          <span class="help-block" style="display: none;">Mật khẩu phải có ít nhất 8 ký tự.</span>
        </div>
        <div class="form-group">
          <label for="phone">Số điện thoại</label>
          <input type="text" class="form-control" name="phone" id="phone" maxlength="10" pattern="^[0-9]{10}$" required>
          <span class="help-block" style="display: none;">Số điện thoại phải gồm 10 chữ số.</span>
        </div>
        <div class="form-group">
          <label for="address">Địa chỉ</label>
          <input type="text" class="form-control" name="address" id="address" maxlength="255" required>
          <span class="help-block" style="display: none;">Vui lòng nhập địa chỉ.</span>
        </div>
        <div class="form-group">
          <label for="age">Tuổi</label>
          <input type="number" class="form-control" name="age" id="age" min="1" max="100" step="1"  required>
          <span class="help-block" style="display: none;">Vui lòng nhập tuổi.</span>
        </div>
        <button type="submit" class="btn btn-success btn-block">Đăng ký</button>
        <p class="text-center" style="margin-top: 10px;">Đã có tài khoản? <span class="toggle-link" onclick="switchForm('login')">Đăng nhập</span></p>
      </form>
   </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script>
   function switchForm(type) {
    $('#loginForm, #registerForm, #forgotForm').addClass('hidden');

    if (type === 'login') {
      $('#loginForm').removeClass('hidden');
    } else if (type === 'register') {
      $('#registerForm').removeClass('hidden');
    } else if (type === 'forgot') {
      $('#forgotForm').removeClass('hidden');
    }
  }

  function showError($input, message) {
    $input.closest('.form-group').addClass('has-error');
    $input.siblings('.help-block').text(message).show();
  }

  function clearErrors(form) {
    form.find('.form-group').removeClass('has-error');
    form.find('.help-block').hide();
  }

  $('#loginForm').on('submit', function(e) {
    e.preventDefault();
    var $form = $(this);
    clearErrors($form);
    var email = $('#loginEmail').val().trim();
    var pass = $('#loginPassword').val();
    var valid = true;

    if (!email || !email.includes('@')) {
      showError($('#loginEmail'), 'Vui lòng nhập email hợp lệ.');
      valid = false;
    }

    if (!pass || pass.length < 8) {
      showError($('#loginPassword'), 'Mật khẩu phải có ít nhất 8 ký tự.');
      valid = false;
    }

    if (valid) {
      $form.off('submit');
      $form.submit();
    }
  });

  $('#registerForm').on('submit', function(e) {
    e.preventDefault();
    var $form = $(this);
    clearErrors($form);
    var name = $('#name').val().trim();
    var email = $('#registerEmail').val().trim();
    var pass = $('#registerPassword').val();
    var phone = $('#phone').val().trim();
    var address = $('#address').val().trim();
    var age = $('#age').val().trim();
    var valid = true;

    if (!name) {
      showError($('#name'), 'Vui lòng nhập tên.');
      valid = false;
    }

    if (!email || !email.includes('@')) {
      showError($('#registerEmail'), 'Vui lòng nhập email hợp lệ.');
      valid = false;
    }

    if (!pass || pass.length < 8) {
      showError($('#registerPassword'), 'Mật khẩu phải có ít nhất 8 ký tự.');
      valid = false;
    }

    if (!phone.match(/^[0-9]{10}$/)) {
      showError($('#phone'), 'Số điện thoại phải gồm 10 chữ số.');
      valid = false;
    }
    if (!address) {
      showError($('#address'), 'Vui lòng địa chỉ.');
      valid = false;
    }
    if (!age) {
      showError($('#age'), 'Vui lòng tuổi.');
      valid = false;
    }
    if (valid) {
      $form.off('submit');
      $form.submit();
    }
  });
</script>
@endsection
