<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class QuestionAdminController extends Controller
{
    public function index()
    {
        $dataQuestions = DB::table('question_request')
        ->join('cities', 'question_request.city_id', '=', 'cities.id')
        ->join('majors', 'question_request.major_id', '=', 'majors.id')
            ->select('question_request.*','cities.name_city','majors.name_major')
            ->orderBy('status_request','asc')
            ->orderByDesc('id')
            ->get();

        return view('admin.pages.question.index', compact('dataQuestions'));
    }

    public function softDeleteQuestion($id)
    {
        DB::table('question_request')
            ->where('id', $id)
            ->update(['status_request' => 1]);

        return back()->with('success', '<PERSON><PERSON><PERSON> câu hỏi thành công');
    }

}
