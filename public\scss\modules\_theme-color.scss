
// Theme color


.scrollToTop {
	background-color: $theme-color;
	border: 2px solid $theme-color;
}

.mu-read-more-btn{
	&:hover,
	&:focus{		
		background-color: $theme-color;
		border-color: $theme-color;
	}
}
.mu-post-btn{
	&:hover,
	&:focus{		
		background-color: $theme-color;
		border-color: $theme-color;			
	}
}
#mu-header{	
	.mu-header-area{		
		.mu-header-top-right{
			.mu-top-social-nav			{				
				li{					
					a{
						&:hover,
						&:focus{
							color: $theme-color;
						}
					}
				}
			}
		}
	}	
}
#mu-menu{
	.navbar-header{
		.navbar-brand{			
			i{
				color: $theme-color;			
			}
		}
	}
	.navbar-default{		
		.navbar-nav{
			li{
				& > a{
					&:hover,
					&:focus{
						border-color: $theme-color;
						color: $theme-color;
					}
				}						
				.dropdown-menu {
					border-top: 2px solid $theme-color;					
					li{
						a{
							&:hover,
							&:focus{
								background-color: $theme-color;																
							}
						}						
					}
				}				
			}
			.open{
				a{
					&:hover,
					&:focus{
						background-color: $theme-color;						
					}
				}
			}
		}
	}
}


#mu-menu .navbar-default .navbar-nav > .active > a, #mu-menu .navbar-default .navbar-nav > .active > a:hover, #mu-menu .navbar-default .navbar-nav > .active > a:focus {  
  color: $theme-color;
  border-bottom: 2px solid $theme-color;
  background-color: transparent;
}
#mu-search{	
	.mu-search-area{
		.mu-search-close{
			background-color: $theme-color;			
		}		
	}
}

#mu-slider{
	.mu-slider-single{
		.mu-slider-content{			
			span{
				background-color: $theme-color;			
			}
		}
	}
	.slick-prev,
	.slick-next {
	  background-color: $theme-color;	 	  
	}
}
#mu-features{
	.mu-features-area{
		.mu-features-content{
			.mu-single-feature{
				span{
					color: $theme-color;
					border: 1px solid $theme-color;									
				}				
				a{
					&:hover,
					&:focus{
						color: $theme-color;
						border-color: $theme-color;
					}
				}
			}
		}
	}
}
#mu-latest-courses{
	.mu-latest-courses-area{		
		.mu-latest-courses-content{						
			.slick-dots{				
				.slick-active{
					background-color: $theme-color;
				}			  
			}
		}		
	}
}

.mu-latest-course-single{
	.mu-latest-course-img{
		
		.mu-latest-course-imgcaption{
			a{
				&:hover,
				&:focus{
					color: $theme-color;
				}
			}			
		}					
	}
	.mu-latest-course-single-content{		
		h4{
			a{
				&:hover,
				&:focus{
					color: $theme-color;
				}
			}
		}
		.mu-latest-course-single-contbottom{
			.mu-course-details{
				color: $theme-color;
			}
		}
	}
}
#mu-our-teacher{
	.mu-our-teacher-area{
		.mu-our-teacher-content{
			.mu-our-teacher-single{
				.mu-our-teacher-img{
					.mu-our-teacher-social{
						background-color: $theme-color;						
						a{
							&:hover,
							&:focus{
								color: $theme-color;
							}
						}
					}
				}			
			}
		}
	}
}

#mu-testimonial{	
	.mu-testimonial-area{
		.mu-testimonial-content{
			.mu-testimonial-item{				
				.mu-testimonial-info{
					span{
						color: $theme-color;
					}
				}
			}
			.slick-dots{
				.slick-active{
					background-color: $theme-color;
				}			  
			}
		}
	}
}
.mu-blog-single-item{
	.mu-blog-single-img{		
		.mu-blog-caption{
			h3{
				a{
					&:hover{
						color: $theme-color;
					}
				}
			}
		}					
	}
	.mu-blog-meta{
		a{
			&:hover,
			&:focus{
				color: $theme-color;
			}
		}		
	}
	.mu-blog-description{
		a{
			&:hover,
			&:focus{
				border-color: $theme-color;
			}
		}
	}
}
#mu-page-breadcrumb{
	.mu-page-breadcrumb-area{
		.breadcrumb{
			.active{
				color: $theme-color;
			}
		}
	}
}
#mu-course-content{
	background-color: #f8f8f8;
	display: inline;
	float: left;
	padding: 100px 0;
	width: 100%;
	.mu-course-content-area{
		display: inline;
		float: left;
		width: 100%;
		.mu-course-container{
			display: inline;
			float: left;
			width: 100%;
			.mu-latest-course-single{
				border: 1px solid #ccc;
				margin-bottom: 30px;
			}
		}	
		.mu-sidebar{
			.mu-single-sidebar{	
				.mu-sidebar-catg{
					li{
						a{
							&:hover,
							&:focus{
								color: $theme-color;							
							}
						}
					}
				}
				.mu-sidebar-popular-courses{
					.media{						
						.media-body{
							.media-heading {
							  a{
							  	&:hover,
							  	&:focus{
							  		color: $theme-color;
							  	}
							  }
							}							
						}
					}
				}
				
				.tag-cloud{
					a{
						&:hover,
						&:focus{
							color: $theme-color;
							border-color: $theme-color;
						}
					}
				}
			}
		}
	}
}
.mu-pagination{
	.pagination{
		li{			
			a{
				&:hover,
				&:focus{
					background-color: $theme-color;
					border-color: $theme-color;
				}			
			}
		}
		.active{
			a{
				background-color: $theme-color;
				border-color: $theme-color;
			}
		}
	}
}
.mu-related-item{
	display: inline;
	float: left;
	margin-top: 30px;
	width: 100%;
	h3{

	}
	.mu-related-item-area{		
		#mu-related-item-slide{
			.slick-prev,
			.slick-next{
				background-color: $theme-color;
			}
		}
	}
}
.mu-blog-single{
	.mu-blog-single-item{		
		.mu-blog-tags{
			.mu-news-single-tagnav{
				li{
					a{
						&:hover,
						&:focus{
							color: $theme-color;
						}
					}
				}
			}
		}
		.mu-blog-social{
			.mu-news-social-nav{
				li{
					a{
						&:hover,
						&:focus{
							background-color: $theme-color;
							border-color: $theme-color;
						}
					}
				}
			}
		}
	}
}
.mu-blog-single-navigation {	
	a{
		&:hover,
		&:focus{
			background-color: $theme-color;
			border-color: $theme-color;
		}
	}
}
.mu-comments-area{
	display: inline;
	float: left;
	width: 100%;
	margin-top: 20px;
	h3{
		margin-bottom: 20px;
		padding: 20px;
		border-bottom: 1px solid #ccc;
		padding-left: 0;
	}
	.comments{
		.commentlist {
			li {
				.reply-btn {					
					&:hover,
					&:focus{				
						background-color: $theme-color;				
						border-color: $theme-color;
					}
				}	
				
				.author-tag {					
					background-color: $theme-color;					
				}				
			}			
		}
		.comments-pagination li a {
			&:hover,
			&:focus{		  
				color: $theme-color;
			}
		}
	}
}
#mu-gallery{
	.mu-gallery-area{
		.mu-gallery-content{
			width: 100%;
			.mu-gallery-top{
				ul{
					li{
						background-color: $theme-color;
						border: 1px solid $theme-color;						
						&:hover,
						&:focus{				
							color: $theme-color;
							border-color: $theme-color;							
						}
					}
					.active{				
						color: $theme-color;
						border-color: $theme-color;						
					}
				}
			}	
			.mu-gallery-body{
				
				ul{					
					li{						
						.mu-single-gallery{
							.mu-single-gallery-item{
								.mu-single-gallery-info{
									.mu-single-gallery-info-inner{										
										a{	
											background-color: $theme-color;											
										}

									}
								}
							}
						}
					}
				}
			}		
		}
	}
}
#mu-contact{
	.mu-contact-area{
		.mu-contact-content{
			.mu-contact-left{
				.contactform{
					input[type="text"],
					input[type="email"],
					input[type="url"] {
						&:focus{
							border-color: $theme-color;
						}						
					}
					textarea {
					 	&:focus{
							border-color: $theme-color;
						}				 	
					}
				}					
			}
		}
	}
}
#mu-error{
	.mu-error-area{
		h2{
			color: $theme-color;			
		}
		
	}
}
#mu-footer{
	.mu-footer-top{
		.mu-footer-top-area{
			.mu-footer-widget{				
				ul{
					li{
						a{
							&:hover,
							&:focus{
								color: $theme-color;
							}
						}
					}
				}				
				.mu-subscribe-form{
					input[type="email"]{
						&:focus{
							border-color: $theme-color;
						}
					}
					.mu-subscribe-btn{
						background-color: $theme-color;						
					}					
				}
			}
		}
	}
}